using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace SuntechApp.Data
{
    public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : IdentityDbContext<ApplicationUser>(options)
    {
    }
    public class SuntechAppDbContext(DbContextOptions<SuntechAppDbContext> options) : DbContext(options)
    {
        public DbSet<Tasks> Tasks { get; set; }
        public DbSet<TaskDetails> TaskDetails { get; set; }
        public DbSet<Employee> Employees { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {

            modelBuilder.Entity<Employee>().ToTable("Employee");
            modelBuilder.Entity<Tasks>().ToTable("Tasks");
            modelBuilder.Entity<TaskDetails>().ToTable("TaskDetails");

        }
    }
    public class RptDbContext(DbContextOptions<RptDbContext> options) : DbContext(options)
    {
        public DbSet<InternalTrxCustomers> InternalTrxCustomers { get; set; }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<InternalTrxCustomers>().ToTable("InternalTrxCustomers");
        }
    }
}