using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Options;

namespace SuntechApp.IpgPlugin;

public class EncryptFilesService(
    ILogger<EncryptFilesService> logger,
    IOptions<IpgJsonSettings> ipgJsonSettings,
    IHttpClientFactory httpClientFactory)
{
    private string? _loginId;
    private DateTime _loginTime;
    private readonly object _loginLock = new();
    private bool _isLoggingIn;
    private readonly TimeSpan _loginExpire = TimeSpan.FromMinutes(25);
    private readonly HttpClient _httpClient = httpClientFactory.CreateClient("Client");
    private readonly IpgJsonSettings _ipgJsonSettings = ipgJsonSettings.Value;
    private readonly ILogger<EncryptFilesService> _logger = logger;

    private async Task<bool> LoginAsync()
    {
        // 使用双重检查锁定模式，避免不必要的锁竞争
        if (IsLoginValid()) return true;

        // 防止并发登录
        lock (_loginLock)
        {
            if (IsLoginValid()) return true;
            if (_isLoggingIn)
            {
                // 如果已经在登录中，等待登录完成
                Monitor.Wait(_loginLock);
                return IsLoginValid();
            }

            _isLoggingIn = true;
        }

        try
        {
            var loginRequest = new IpgJsonLoginRequest
            {
                Name = _ipgJsonSettings.LoginName,
                Password = _ipgJsonSettings.Password
            };

            string jsonRequest = JsonSerializer.Serialize(loginRequest);
            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                _ipgJsonSettings.IpgJsonUrl + "login",
                content
            );

            if (response.IsSuccessStatusCode)
            {
                string jsonResponse = await response.Content.ReadAsStringAsync();
                var loginResponse = JsonSerializer.Deserialize<IpgJsonResponse>(jsonResponse);

                if (loginResponse is not null && loginResponse.error == "0")
                {
                    lock (_loginLock)
                    {
                        _loginId = loginResponse.loginid;
                        _loginTime = DateTime.Now;
                        _logger.LogInformation($"登录成功! LoginID:{_loginId}---LoginTime:{_loginTime}");
                        return true;
                    }
                }
            }

            _logger.LogWarning("登录失败");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError($"登录过程中发生异常：{ex.Message}");
            return false;
        }
        finally
        {
            lock (_loginLock)
            {
                _isLoggingIn = false;
                Monitor.PulseAll(_loginLock); // 通知等待的线程
            }
        }
    }

    private async Task LogoutAsync()
    {
        string? loginIdToLogout;

        // 原子地获取并清除登录状态
        lock (_loginLock)
        {
            if (string.IsNullOrEmpty(_loginId)) return;
            loginIdToLogout = _loginId;
            _loginId = null;
        }

        try
        {
            var logoutRequest = new IpgJsonLogoutRequest { LoginID = loginIdToLogout };
            string jsonRequest = JsonSerializer.Serialize(logoutRequest);
            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                _ipgJsonSettings.IpgJsonUrl + "logout",
                content
            );

            string jsonResponse = await response.Content.ReadAsStringAsync();
            var logoutResponse = JsonSerializer.Deserialize<IpgJsonResponse>(jsonResponse);

            if (logoutResponse != null && logoutResponse.error == "0")
            {
                _logger.LogInformation("注销成功");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"注销过程中发生异常：{ex.Message}");
        }
    }

    private bool IsLoginValid()
    {
        lock (_loginLock)
        {
            return !string.IsNullOrEmpty(_loginId) && (DateTime.Now - _loginTime) < _loginExpire;
        }
    }

    public async Task EncryptFilesFromPath(List<string>? filesToEncrypt, int maxRetryCount = 3)
    {
        if (filesToEncrypt is null || filesToEncrypt.Count == 0)
        {
            _logger.LogWarning("没有需要加密的文件");
            return;
        }

        int retryCount = 0;
        bool shouldRetry;

        do
        {
            shouldRetry = false;

            // 检查登录状态，如果未登录或已过期则重新登录
            if (!IsLoginValid())
            {
                if (!await LoginAsync())
                {
                    _logger.LogError("登录失败，无法继续加密文件");
                    return;
                }
            }

            string? currentLoginId;
            lock (_loginLock)
            {
                currentLoginId = _loginId;
            }

            if (string.IsNullOrEmpty(currentLoginId))
            {
                _logger.LogError("登录ID无效");
                return;
            }

            try
            {
                var encryptRequest = new IpgJsonEncryptFileRequest
                {
                    LoginID = currentLoginId,
                    Param = new IpgJsonEncryptFileRequestParam()
                    {
                        Files = filesToEncrypt
                    }
                };

                string jsonRequest = JsonSerializer.Serialize(encryptRequest);
                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                // 发送加密请求
                var response = await _httpClient.PostAsync(
                    _ipgJsonSettings.IpgJsonUrl + "encryptFile",
                    content
                );

                string jsonResponse = await response.Content.ReadAsStringAsync();
                var encryptResponse = JsonSerializer.Deserialize<IpgJsonResponse>(jsonResponse);

                if (encryptResponse is not null)
                {
                    if (encryptResponse.error == "0")
                    {
                        _logger.LogInformation("文件加密成功!");
                        return; // 成功完成，直接返回
                    }

                    if (encryptResponse.error == "61453") // 登录过期
                    {
                        _logger.LogWarning("登录已过期，正在尝试重新登录...");
                        await LogoutAsync(); // 先登出
                        shouldRetry = retryCount < maxRetryCount; // 检查是否还可以重试
                        if (shouldRetry)
                        {
                            retryCount++;
                            _logger.LogInformation($"正在重试第 {retryCount} 次...");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"文件加密失败, 错误: {encryptResponse.desc}");
                        if (encryptResponse.failes != null && encryptResponse.failes.Count > 0)
                        {
                            foreach (var failedItem in encryptResponse.failes)
                            {
                                _logger.LogWarning($"失败文件: {failedItem.item}, 原因: {failedItem.desc}");
                            }
                        }
                        return; // 其他错误直接返回
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"加密过程中发生异常：{ex.Message}");
                if (retryCount < maxRetryCount)
                {
                    shouldRetry = true;
                    retryCount++;
                    _logger.LogInformation($"发生异常，正在重试第 {retryCount} 次...");
                }
                else
                {
                    throw; // 重试次数用尽，抛出异常
                }
            }
        } while (shouldRetry);

        _logger.LogError($"加密失败，已达到最大重试次数 {maxRetryCount + 1} 次");
    }


    // 加密指定路径下的所有文件
    public async Task EncryptFilesFromFolder(string folderPath=@"C:\idk", string searchPattern = "*", int maxRetryCount = 3)
    {
        if (string.IsNullOrWhiteSpace(folderPath))
        {
            _logger.LogWarning("文件夹路径不能为空");
            return;
        }

        if (!Directory.Exists(folderPath))
        {
            _logger.LogWarning($"文件夹路径不存在: {folderPath}");
            return;
        }

        try
        {
            var files = Directory.EnumerateFiles(folderPath, searchPattern)
                .Select(Path.GetFullPath)
                .ToList();

            if (files.Count == 0)
            {
                _logger.LogWarning($"在路径 {folderPath} 中没有找到匹配的文件");
                return;
            }

            _logger.LogInformation($"在路径 {folderPath} 中找到 {files.Count} 个文件");
            await EncryptFilesFromPath(files, maxRetryCount);
        }
        catch (Exception ex)
        {
            _logger.LogError($"读取文件夹时发生异常：{ex.Message}");
        }
    }


    // 加密单个文件
    public async Task EncryptSingleFile(string filePath, int maxRetryCount = 3)
    {
        if (string.IsNullOrWhiteSpace(filePath))
        {
            _logger.LogWarning("文件路径不能为空");
            return;
        }

        if (!File.Exists(filePath))
        {
            _logger.LogWarning($"文件不存在: {filePath}");
            return;
        }

        await EncryptFilesFromPath(new List<string> { Path.GetFullPath(filePath) }, maxRetryCount);
    }
}
