using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

public static class PrintHelper
{
    /// <summary>
    /// ֱ���������ӡ�������ı�
    /// </summary>
    /// <param name="printerIp">��ӡ��IP</param>
    /// <param name="port">��ӡ���˿ڣ�ͨ��Ϊ9100</param>
    /// <param name="content">��ӡ����</param>
    public static async Task PrintTextAsync(string printerIp, int port, string content)
    {
        using var client = new TcpClient();
        await client.ConnectAsync(printerIp, port);
        using var stream = client.GetStream();
        byte[] data = Encoding.UTF8.GetBytes(content + "\n");
        await stream.WriteAsync(data, 0, data.Length);
        await stream.FlushAsync();
    }
}
