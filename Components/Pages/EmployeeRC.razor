@page "/EmployeeRC"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using SuntechApp.Data
@using Microsoft.EntityFrameworkCore
@inject EmployeeService EmployeeService

<MudDataGrid T="Employee" Items="@employees" Groupable="false" SortMode="SortMode.Multiple" Filterable="true" QuickFilter="@_quickFilter" Hover="@_hover" ReadOnly="@_readOnly" EditMode="@(_isCellEditMode ? DataGridEditMode.Cell : DataGridEditMode.Form)" Bordered="true" Dense="true" EditTrigger="@(_editTriggerRowClick ? DataGridEditTrigger.OnRowClick : DataGridEditTrigger.Manual)" StartedEditingItem="StartedEditingItem" CanceledEditingItem="@CanceledEditingItem" CommittedItemChanges="@CommittedItemChanges">
    <ToolBarContent>
        <MudText Typo="Typo.h6">雇员</MudText>
        <MudSpacer/>
        <MudTextField @bind-Value="_searchString" Placeholder="Search" Adornment="Adornment.Start" Immediate="true"
        AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
        <MudButton OnClick="@AddItem" Color="@Color.Success" Class="add-item-btn">Add Item</MudButton>
    </ToolBarContent>

    <Columns>
        <PropertyColumn Property="x => x.Id" Editable="false" />
        <PropertyColumn Property="x => x.Name" SortBy="@_sortBy" />
        <PropertyColumn Property="x => x.Department"/>
        <PropertyColumn Property="x => x.Salary"/>
        <TemplateColumn CellClass="d-flex" >
            <CellTemplate>

                <MudIconButton Size="@Size.Small"  Variant="Variant.Outlined" Color="Color.Primary" Icon="@Icons.Material.Filled.Edit" OnClick="@context.Actions.StartEditingItemAsync" />
                
                <MudIconButton Size="@Size.Small"  Variant="Variant.Outlined" Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteEmployee(context))" />

            </CellTemplate>
        </TemplateColumn>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="Employee" />
    </PagerContent>
</MudDataGrid>
<div class="d-flex flex-wrap mt-4">
    <MudSwitch @bind-Value="@_sortNameByLength" Color="Color.Primary">Sort Name Column By Length</MudSwitch>
    <MudSwitch @bind-Value="_hover" Color="Color.Primary">Hover</MudSwitch>
    <MudSwitch @bind-Value="_readOnly" Color="Color.Primary">Read Only</MudSwitch>
    <MudSwitch @bind-Value="_isCellEditMode" Color="Color.Primary">Cell</MudSwitch>
    <MudSwitch @bind-Value="_editTriggerRowClick" Color="Color.Primary">On Row Click</MudSwitch>
</div>

@code {
    private List<Employee> employees = new List<Employee>();
    private string? _searchString;
    private bool _sortNameByLength;
    private bool _hover;
    private bool _readOnly;
    private bool _isCellEditMode;
    private bool _editTriggerRowClick;

    protected override async Task OnInitializedAsync()
    {
        employees = await EmployeeService.GetEmployeesAsync();
    }
    private async Task DeleteEmployee(CellContext<Employee> context)
    {
        var employee = context.Item as Employee;
        await EmployeeService.DeleteEmployeeAsync(employee.Id);
        employees = await EmployeeService.GetEmployeesAsync();
    }
    private void AddItem()
    {
        employees.Add(new Employee { Id = 0,Name = "",Department = "",Salary = 0 });
    }
    void StartedEditingItem(Employee item)
    {

    }

    void CanceledEditingItem(Employee item)
    {

    }

    async Task CommittedItemChanges(Employee item)
    {
        await EmployeeService.UpdateEmployeeAsync(item);
    }
    private Func<Employee, bool> _quickFilter => x =>
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        if ((x.Department??"").Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if ((x.Name?? "").Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if ($"{x.Department} {x.Name} {x.Salary}".Contains(_searchString))
            return true;

        return false;
    };
    private Func<Employee, object> _sortBy => x =>
    {
        if (_sortNameByLength)
            return (x.Name?? "").Length;
        else
            return x.Name ?? "";
    };
}