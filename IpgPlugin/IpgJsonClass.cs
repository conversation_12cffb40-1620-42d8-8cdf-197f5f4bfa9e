namespace SuntechApp.IpgPlugin
{
    public class IpgJsonSettings
    {
        public string? LoginName { get; set; }
        public string? Password { get; set; }
        public string? IpgJsonUrl { get; set; }
    }
    public class IpgJsonLoginRequest
    {
        public string? Name { get; set; }
        public string? Password { get; set; }
    }
    public class IpgJsonLogoutRequest
    {
        public required string? LoginID { get; set; }
    }
    public class IpgJsonEncryptFileRequest
    {
        public string? LoginID { get; set; }
        public object? Param { get; set; }
    }
    public class IpgJsonDecryptFileRequest
    {
        public string? LoginID { get; set; }
        public string? Func { get; set; } = "DecryptFile";
        public object? Param { get; set; }
    }
    public class IpgJsonEncryptFileRequestParam
    {
        public List<string>? Files { get; set; }
    }
    public class IpgJsonDecryptFileRequestParam
    {
        public List<string>? Files { get; set; }
        public bool Compress { get; set; } = false;
    }

    public class IpgJsonResponse
    {
        public string? error { get; set; }
        public string? loginid { get; set; }
        public List<FailedItem>? failes { get; set; }
        public string? desc { get; set; }
    }
    public class FailedItem
    {
        public string? item { get; set; }
        public string? desc { get; set; }
    }
}
