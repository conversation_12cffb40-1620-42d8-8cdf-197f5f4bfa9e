@page "/ExamManagement"
@using Microsoft.AspNetCore.Authorization
@using SqlSugar
@using SuntechApp.Data
@attribute [Authorize]
@inject ISqlSugarClient db
@inject ISnackbar Snackbar
@inject IDialogService DialogService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer

<PageTitle>试卷管理</PageTitle>

    @* <MudPaper MaxWidth="MaxWidth.ExtraLarge" Class="pa-4" Elevation="3"> *@
        <MudDataGrid T="ExamPaper" Items="@examPapers" Groupable="false" 
                     SortMode="SortMode.Multiple" Filterable="true" 
                     QuickFilter="@_quickFilter" Hover="true" Bordered="true" Dense="true">
            <ToolBarContent>
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.List" Class="mr-2" Color="Color.Primary" />
                    <MudText Typo="Typo.h6">试卷列表</MudText>
                    @if (selectedPapers.Count > 0)
                    {
                        <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-3">
                            已选择 @selectedPapers.Count 项
                        </MudChip>
                    }
                </div>
                <MudSpacer />
                <div class="d-flex align-center gap-3">
                    @if (selectedPapers.Count > 0)
                    {
                        <MudButtonGroup Variant="Variant.Outlined" Size="Size.Small">
                            <MudButton Color="Color.Success"
                                     StartIcon="@Icons.Material.Filled.PlayArrow"
                                     OnClick="BatchEnablePapers">
                                批量启用
                            </MudButton>
                            <MudButton Color="Color.Warning"
                                     StartIcon="@Icons.Material.Filled.Pause"
                                     OnClick="BatchDisablePapers">
                                批量禁用
                            </MudButton>
                            <MudButton Color="Color.Error"
                                     StartIcon="@Icons.Material.Filled.Delete"
                                     OnClick="BatchDeletePapers">
                                批量删除
                            </MudButton>
                        </MudButtonGroup>
                        <MudDivider Vertical="true" FlexItem="true" />
                    }
                    <MudTextField @bind-Value="_searchString"
                                Placeholder="搜索试卷标题或文件名..."
                                Adornment="Adornment.Start"
                                Immediate="true"
                                AdornmentIcon="@Icons.Material.Filled.Search"
                                IconSize="Size.Medium"
                                Variant="Variant.Outlined"
                                Margin="Margin.Dense"
                                Class="mt-0" />
                    <MudButton OnClick="@ShowUploadDialog"
                             Color="@Color.Success"
                             StartIcon="@Icons.Material.Filled.Upload"
                             Variant="Variant.Filled"
                             Size="Size.Medium">
                        上传试卷
                    </MudButton>
                </div>
            </ToolBarContent>
            <Columns>
                <TemplateColumn Title="" Sortable="false" Filterable="false" Style="width: 50px;">
                    <HeaderTemplate>
                        <MudCheckBox T="bool"
                                   Value="@isAllSelected"
                                   ValueChanged="@ToggleSelectAll"
                                   TriState="true"
                                   Size="Size.Small" />
                    </HeaderTemplate>
                    <CellTemplate>
                        <MudCheckBox T="bool"
                                   Value="@selectedPapers.Contains(context.Item.Id)"
                                   ValueChanged="@((bool value) => ToggleSelection(context.Item.Id, value))"
                                   Size="Size.Small" />
                    </CellTemplate>
                </TemplateColumn>
                <PropertyColumn Property="x => x.Id" Title="ID" />
                <PropertyColumn Property="x => x.Title" Title="试卷标题" />
                <PropertyColumn Property="x => x.FileName" Title="文件名" />
                <PropertyColumn Property="x => x.UploadedDate" Title="上传时间" Format="yyyy-MM-dd HH:mm" />
                <PropertyColumn Property="x => x.IsActive" Title="状态">
                    <CellTemplate>
                        <MudChip Color="@(context.Item.IsActive ? Color.Success : Color.Default)" 
                               Size="Size.Small">
                            @(context.Item.IsActive ? "启用" : "禁用")
                        </MudChip>
                    </CellTemplate>
                </PropertyColumn>
                <TemplateColumn Title="操作" Sortable="false">
                    <CellTemplate>
                        <div class="d-flex gap-2">
                            <MudTooltip Text="预览试卷">
                                <MudIconButton Icon="@Icons.Material.Filled.Preview"
                                             Color="Color.Info"
                                             Size="Size.Small"
                                             OnClick="@(() => PreviewPaper(context.Item))" />
                            </MudTooltip>
                            <MudTooltip Text="下载试卷">
                                <MudIconButton Icon="@Icons.Material.Filled.Download"
                                             Color="Color.Primary"
                                             Size="Size.Small"
                                             OnClick="() => DownloadPaper(context.Item)" />
                            </MudTooltip>
                            <MudTooltip Text="@(context.Item.IsActive ? "禁用试卷" : "启用试卷")">
                                <MudIconButton Icon="@(context.Item.IsActive ? Icons.Material.Filled.Pause : Icons.Material.Filled.PlayArrow)"
                                             Color="@(context.Item.IsActive ? Color.Warning : Color.Success)"
                                             Size="Size.Small"
                                             OnClick="() => ToggleStatus(context.Item)" />
                            </MudTooltip>
                            <MudTooltip Text="删除试卷">
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                             Color="Color.Error"
                                             Size="Size.Small"
                                             OnClick="() => DeletePaper(context.Item)" />
                            </MudTooltip>
                        </div>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
            <PagerContent>
                <MudDataGridPager PageSizeOptions="new int[]{10, 25, 50, 100}" />
            </PagerContent>
        </MudDataGrid>
    @* </MudPaper> *@

@code {
    private SqlSugarProvider DefaultDb => db.AsTenant().GetConnection("Default");
    private List<ExamPaper> examPapers = new();
    private string _searchString = "";

    // 批量操作相关变量
    private HashSet<int> selectedPapers = new();
    private bool isAllSelected = false;
    


    protected override async Task OnInitializedAsync()
    {
        await LoadExamPapers();
    }

    private Func<ExamPaper, bool> _quickFilter => x =>
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        if (x.Title.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if (x.FileName.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        return false;
    };

    private async Task LoadExamPapers()
    {
        try
        {
            examPapers = await DefaultDb.Queryable<ExamPaper>()
                .OrderByDescending(x => x.UploadedDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载试卷列表失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task ShowUploadDialog()
    {
        var parameters = new DialogParameters<UploadPaperDialog>();
        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.Medium,
            FullWidth = true,
            CloseButton = true
        };

        var dialog = await DialogService.ShowAsync<UploadPaperDialog>("上传试卷", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadExamPapers();
        }
    }



    private async Task ToggleStatus(ExamPaper paper)
    {
        try
        {
            paper.IsActive = !paper.IsActive;
            
            await DefaultDb.Updateable(paper)
                .UpdateColumns(x => x.IsActive)
                .ExecuteCommandHasChangeAsync();
            
            Snackbar.Add($"试卷已{(paper.IsActive ? "启用" : "禁用")}", Severity.Success);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
            paper.IsActive = !paper.IsActive; // 回滚状态
        }
    }

    private async Task DeletePaper(ExamPaper paper)
    {
        bool? result = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除试卷 \"{paper.Title}\" 吗？此操作不可撤销。",
            yesText: "删除", cancelText: "取消");

        if (result == true)
        {
            try
            {
                // 删除数据库记录
                await DefaultDb.Deleteable<ExamPaper>().In(paper.Id).ExecuteCommandAsync();
                
                // 删除文件
                if (File.Exists(paper.FilePath))
                {
                    File.Delete(paper.FilePath);
                }
                
                Snackbar.Add("试卷删除成功", Severity.Success);
                await LoadExamPapers();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task PreviewPaper(ExamPaper paper)
    {
        var parameters = new DialogParameters<PaperPreviewDialog>
        {
            { nameof(PaperPreviewDialog.Paper), paper }
        };

        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.ExtraLarge,
            FullWidth = true,
            CloseButton = true,
            FullScreen = false
        };

        await DialogService.ShowAsync<PaperPreviewDialog>("试卷预览", parameters, options);
    }

    private async Task DownloadPaper(ExamPaper paper)
    {
        try
        {
            var url = $"/api/ExamFile/download-paper/{Path.GetFileName(paper.FilePath)}";
            await JSRuntime.InvokeVoidAsync("open", url, "_blank");
            Snackbar.Add("下载已开始，请查看浏览器下载内容", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"下载失败: {ex.Message}", Severity.Error);
        }
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
        return $"{bytes / (1024 * 1024):F1} MB";
    }

    // 批量操作方法
    private void ToggleSelection(int paperId, bool isSelected)
    {
        if (isSelected)
        {
            selectedPapers.Add(paperId);
        }
        else
        {
            selectedPapers.Remove(paperId);
        }

        // 更新全选状态
        isAllSelected = selectedPapers.Count == examPapers.Count;
        StateHasChanged();
    }

    private void ToggleSelectAll(bool selectAll)
    {
        if (selectAll)
        {
            selectedPapers = examPapers.Select(p => p.Id).ToHashSet();
            isAllSelected = true;
        }
        else
        {
            selectedPapers.Clear();
            isAllSelected = false;
        }
        StateHasChanged();
    }

    private async Task BatchEnablePapers()
    {
        if (selectedPapers.Count == 0) return;

        bool? result = await DialogService.ShowMessageBox(
            "确认批量启用",
            $"确定要启用选中的 {selectedPapers.Count} 个试卷吗？",
            yesText: "启用", cancelText: "取消");

        if (result == true)
        {
            try
            {
                await DefaultDb.Updateable<ExamPaper>()
                    .SetColumns(x => x.IsActive == true)
                    .Where(x => selectedPapers.Contains(x.Id))
                    .ExecuteCommandAsync();

                Snackbar.Add($"成功启用 {selectedPapers.Count} 个试卷", Severity.Success);
                selectedPapers.Clear();
                isAllSelected = false;
                await LoadExamPapers();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"批量启用失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task BatchDisablePapers()
    {
        if (selectedPapers.Count == 0) return;

        bool? result = await DialogService.ShowMessageBox(
            "确认批量禁用",
            $"确定要禁用选中的 {selectedPapers.Count} 个试卷吗？",
            yesText: "禁用", cancelText: "取消");

        if (result == true)
        {
            try
            {
                await DefaultDb.Updateable<ExamPaper>()
                    .SetColumns(x => x.IsActive == false)
                    .Where(x => selectedPapers.Contains(x.Id))
                    .ExecuteCommandAsync();

                Snackbar.Add($"成功禁用 {selectedPapers.Count} 个试卷", Severity.Success);
                selectedPapers.Clear();
                isAllSelected = false;
                await LoadExamPapers();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"批量禁用失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task BatchDeletePapers()
    {
        if (selectedPapers.Count == 0) return;

        bool? result = await DialogService.ShowMessageBox(
            "确认批量删除",
            $"确定要删除选中的 {selectedPapers.Count} 个试卷吗？此操作不可撤销。",
            yesText: "删除", cancelText: "取消");

        if (result == true)
        {
            try
            {
                // 获取要删除的试卷信息（用于删除文件）
                var papersToDelete = await DefaultDb.Queryable<ExamPaper>()
                    .Where(x => selectedPapers.Contains(x.Id))
                    .ToListAsync();

                // 删除数据库记录
                await DefaultDb.Deleteable<ExamPaper>()
                    .Where(x => selectedPapers.Contains(x.Id))
                    .ExecuteCommandAsync();

                // 删除文件
                foreach (var paper in papersToDelete)
                {
                    if (File.Exists(paper.FilePath))
                    {
                        try
                        {
                            File.Delete(paper.FilePath);
                        }
                        catch (Exception ex)
                        {
                            // 记录文件删除失败，但不影响整体操作
                            Console.WriteLine($"删除文件失败: {paper.FilePath}, 错误: {ex.Message}");
                        }
                    }
                }

                Snackbar.Add($"成功删除 {selectedPapers.Count} 个试卷", Severity.Success);
                selectedPapers.Clear();
                isAllSelected = false;
                await LoadExamPapers();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"批量删除失败: {ex.Message}", Severity.Error);
            }
        }
    }
}
