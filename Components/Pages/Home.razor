@page "/"
@using System.Linq
@using SuntechApp.Data
@using SqlSugar

@inject ISqlSugarClient db

<style>
    .welcome-header {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        border-radius: 12px;
        padding: 1rem;
        color: white;
        margin-top: -1rem;
        margin-bottom: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .card-base {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        border: 1px solid #e5e7eb;
    }

    .card-hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-color: #4f46e5;
    }

    /* 确保表格容器正确填充可用空间 */
    .mud-table-container {
        display: flex;
        flex-direction: column;
        flex: 1;
        height: 100%;
    }


</style>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="py-4">
    <!-- Welcome Header -->
    <div class="welcome-header">
        <MudText Typo="Typo.h6" Class="font-weight-bold mb-1">欢迎回来！</MudText>
        <MudText Typo="Typo.body1" Class="text-white-50">今天是 @DateTime.Now.ToString("yyyy年MM月dd日 dddd")</MudText>
    </div>

    <!-- Quick Access Cards -->
    @* <MudText Typo="Typo.body1" Class="mb-2 text-grey-darken-2">快捷入口</MudText> *@
    <MudGrid Spacing="1" Class="mb-3">
        @foreach (var (card, index) in cards.Select((card, index) => (card, index))) {
            <MudItem xs="6" sm="4" md="3" lg="2">
                <MudLink Href="@card.Href" Underline="Underline.None" Class="h-100">
                    <MudCard Class="@card.CardClass"
                             Elevation="0"
                             Style="padding: 0.6rem;"
                             @onmouseenter="@(() => OnMouseEnter(index))"
                             @onmouseleave="@(() => OnMouseLeave(index))">
                        <MudText Typo="Typo.body2"
                                 Class="font-weight-medium text-grey-darken-3 text-center"
                                 Style="line-height: 1.1;">
                            @card.Title
                        </MudText>
                    </MudCard>
                </MudLink>
            </MudItem>
        }
    </MudGrid>
    <!-- 公告 -->
    <div class="news-section">
        <MudGrid Spacing="3">
            <!-- 左列 公告Table -->
            <MudCard Elevation="2" Style="width: 50%; height:71vh; margin-top: 10px;">
                <MudCardContent Style="overflow-y:auto;width:100%;">
                    <MudTable Items="news" Style="width: 100%; max-height:69vh;" Virtualize="true"
                                  Dense="true"
                                  Hover="true"
                                  Striped="false"
                                  LoadingProgressColor="Color.Primary"
                                  Loading="@isLoading">
                        <!-- 表格内容保持不变 -->
                        <HeaderContent>
                            <MudTh style="font-weight: bold">最新公告</MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="文章标题" Style="width: 100%;">
                                <MudLink Href="@($"news/{context.NewsID}")" Underline="Underline.None"
                                         Class="w-100">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <MudText Typo="Typo.body1" Class="text-truncate" Style="max-width: 70%;">@context.Title</MudText>
                                        <MudText Typo="Typo.body2"
                                                 Color="Color.Dark">@context.PublishedDate?.ToString("yyyy-MM-dd")</MudText>
                                    </div>
                                </MudLink>
                            </MudTd>
                        </RowTemplate>
                        <NoRecordsContent>
                            <MudTd ColSpan="1" Class="text-center py-4">
                                <MudIcon Icon="@Icons.Material.Filled.Info" Class="mr-2"/>
                                暂无公告
                            </MudTd>
                        </NoRecordsContent>
                        <PagerContent>
                            <MudTablePager HorizontalAlignment="HorizontalAlignment.Center"
                                           PageSizeOptions="new int[] {10}"
                                           RowsPerPageString="每页行数"
                                            />
                        </PagerContent>
                    </MudTable>
                </MudCardContent>
            </MudCard>

            <!-- 右列预览 -->
    
            <MudCard Elevation="2" Style="width: 50%;height:71vh;margin-top: 10px;">
                    @if (selectedArticle is not null) {
                        <MudCardHeader Class="flex-shrink-0">
                            <div class="w-100">
                                <MudText Typo="Typo.h6" Class="font-weight-bold">@selectedArticle.Title</MudText>
                                <MudText Typo="Typo.caption" Class="text-grey mt-1 d-block">
                                    发布时间: @selectedArticle.PublishedDate?.ToString("yyyy-MM-dd") |
                                    作者: @selectedArticle.Author
                                </MudText>
                            </div>
                        </MudCardHeader>
                        <MudCardContent Class="flex-grow-1" Style="overflow-y: auto; max-height: 500px;">
                            <MudText Style="white-space:pre-line;">"@selectedArticle?.Content"</MudText>
                        </MudCardContent>
                        <MudCardActions Class="flex-shrink-0">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       EndIcon="@Icons.Material.Filled.ArrowForward"
                                       Href="@($"news/{selectedArticle.NewsID}")">
                                阅读全文
                            </MudButton>
                        </MudCardActions>
                    }
                </MudCard>
           
        </MudGrid>
    </div>
</MudContainer>

@code {
    private List<News> news = new();
    private News selectedArticle;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync() {
        try {
            news = await db.AsTenant().QueryableWithAttr<News>()
                .Select(n => new News {
                    NewsID = n.NewsID,
                    Title = n.Title,
                    PublishedDate = n.PublishedDate
                })
                .OrderByDescending(n => n.PublishedDate)
                .ToListAsync();

            // 从数据库获取最近一篇
            if (news.Any())
            {
                selectedArticle = await db.AsTenant().QueryableWithAttr<News>()
                .OrderByDescending(n => n.PublishedDate)
                .FirstAsync();
            }
        }
        finally {
            isLoading = false;
        }
    }


    private List<CardState> cards = new() {
        new CardState {
            Href = "http://erp.suntechoa.cn/WsWebClient/default.aspx",
            Icon = Icons.Material.Filled.Dashboard,
            Title = "ERP系统"
        },
        new CardState {
            Href = "http://************:5301/login#120",
            Icon = Icons.Material.Filled.Assessment,
            Title = "IPQC系统"
        },
        new CardState {
            Href = "http://************/BPM/YZSoft/login/2020/?ReturnUrl=%2fbpm%2f",
            Icon = Icons.Material.Filled.Settings,
            Title = "BPM系统"
        },
        new CardState {
            Href = "https://work.weixin.qq.com/mail/",
            Icon = Icons.Material.Filled.Email,
            Title = "企业邮箱"
        },
        new CardState {
            Href = "Materials",
            Icon = Icons.Material.Filled.MenuBook,
            Title = "培训资料"
        },
        new CardState {
            Href = "http://www.suntechceramics.com/",
            Icon = Icons.Material.Filled.Public,
            Title = "公司官网"
        }
    };

    private void OnMouseEnter(int index) {
        cards[index].IsHovered = true;
        StateHasChanged();
    }

    private void OnMouseLeave(int index) {
        cards[index].IsHovered = false;
        StateHasChanged();
    }

    private class CardState {
        public string Href { get; set; }
        public string Icon { get; set; }
        public string Title { get; set; }
        public bool IsHovered { get; set; }
        public string CardClass => IsHovered ? "card-base card-hover" : "card-base";
    }

}