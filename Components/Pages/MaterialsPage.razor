@page "/MaterialsPage/{id}"  
@using SuntechApp.Data  
@using SqlSugar  
@inject ISqlSugarClient db  

<style>
    .breadcrumbs-small {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        min-height: 16px !important;
        font-size: 0.85rem !important;
        line-height: 1.2 !important;
    }

        .breadcrumbs-small .mud-breadcrumbs-item {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            min-height: 0 !important;
            font-size: 0.85rem !important;
            line-height: 1.2 !important;
        }

        .breadcrumbs-small .mud-link {
            padding: 0 2px !important;
            font-size: 0.85rem !important;
            line-height: 1.2 !important;
        }
</style>

<MudBreadcrumbs Items="_items" Separator=">" Class="breadcrumbs-small">
    <ItemTemplate Context="item">
        <MudLink Href="@item.Href">@item.Text</MudLink>
    </ItemTemplate>
</MudBreadcrumbs>
<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="py-4">
<MudPaper Class="pa-4" Style="height:100%;">  
    <iframe src=@pdfUrl style="width:100%;height:78vh;" frameborder="0"></iframe>  
</MudPaper>
</MudContainer>

@code {  
    [Parameter]  
    public string? id { get; set; }  

    private int? _id;  

    private string? pdfUrl = "";  

    private TrainingMaterial? trainingMaterial { get; set; }  
    private List<BreadcrumbItem> _items = new();
    
    protected override async Task OnParametersSetAsync()  
    {  
        if (int.TryParse(id, out int parsedId))  
        {  
            _id = parsedId;  
            trainingMaterial = await db.AsTenant().QueryableWithAttr<TrainingMaterial>()  
                                .Where(n => n.Id == _id)  
                                .FirstAsync();  
            
            if (trainingMaterial != null)
            {
                pdfUrl = Path.Combine("/TrainingMaterial", trainingMaterial.FileID![..4], trainingMaterial.FileID![4..]);
                
                // 获取分类信息
                var parentCategory = await db.AsTenant().QueryableWithAttr<TrainingMaterialClass>()
                    .FirstAsync(x => x.Id == trainingMaterial.ParentId);
                
                // 构建面包屑
                _items.Clear();
                _items.Add(new BreadcrumbItem("首页", "/"));
                _items.Add(new BreadcrumbItem("培训资料", "/Materials"));
                
                // 如果有父级分类，添加到面包屑
                if (parentCategory != null)
                {
                    // 如果父级分类还有父级
                    if (parentCategory.ParentId > 0)
                    {
                        var grandParent = await db.AsTenant().QueryableWithAttr<TrainingMaterialClass>()
                            .FirstAsync(x => x.Id == parentCategory.ParentId);
                        if (grandParent != null)
                        {
                            _items.Add(new BreadcrumbItem(grandParent.Class, $"/Materials/{grandParent.Id}", disabled: true));
                            
                        }
                    }
                    
                    _items.Add(new BreadcrumbItem(parentCategory.Class, $"/Materials/{parentCategory.Id}"));
                }
                
                // 添加当前页面
                _items.Add(new BreadcrumbItem(trainingMaterial.Title, $"/MaterialsPage/{_id}"));
                
            }
        }  
    }  
}
