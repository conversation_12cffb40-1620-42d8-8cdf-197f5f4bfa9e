@page "/MaterialsTest"
@using SuntechApp.Data
@using SqlSugar
@inject ISqlSugarClient db
@inject IJSRuntime JSRuntime

<script src="js/epubInterop.js"></script>


<MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
    <MudTabPanel Text="信息化资料">
        <MudTable Items="news" Virtualize="true" OverscanCount="20" Hover="true" SortLabel="Sort By" Elevation="1" AllowUnsorted="false" Dense="true">
            <HeaderContent>
                <MudTh style="width:10%">发布日期</MudTh>
                <MudTh style="width:10%">作者</MudTh>
                <MudTh style="width:80%">标题</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="发布日期"><a href="@($"news/{context.NewsID}")">@context.PublishedDate?.ToString("yyyy-MM-dd")</a></MudTd>
                <MudTd DataLabel="作者"><a href="@($"news/{context.NewsID}")">@context.Author</a></MudTd>
                <MudTd DataLabel="标题"><a href="@($"news/{context.NewsID}")">@context.Title</a></MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager PageSizeOptions="new int[] { 10, 25, 50, 100 }" />
            </PagerContent>
        </MudTable>
    </MudTabPanel>
    <MudTabPanel Text="人事资料">  
        <iframe src="/files/阿里云域名续费发票.pdf" style="width:100%;height:700px;" frameborder="0"></iframe>
    </MudTabPanel>
    <MudTabPanel Text="工程资料">
        <div id="epub-iframe-container" style="width:100%;height:700px;"></div>
        <MudButton OnClick="ShowEpub">在线预览EPUB文档</MudButton>
    </MudTabPanel>
    <MudTabPanel Text="研发资料">
        <MudText>Content Disabled</MudText>
    </MudTabPanel>
</MudTabs>



@code {
    private List<News> news = new List<News>();
    private string pdfUrl = "C:\\12\\HTTP接口系统使用说明.pdf"; // 替换为实际的PDF文件URL
    
    protected override async Task OnInitializedAsync()
    {
        news = await db.AsTenant().QueryableWithAttr<News>()
            .OrderBy(n => n.PublishedDate, OrderByType.Desc)
            .ToListAsync();
    }

    private async Task ShowEpub()
    {
        await JSRuntime.InvokeVoidAsync("epubInterop.openEpubInIframe", "/12/别让猴子跳回背上.epub", "epub-iframe-container");
    }
}

