using Microsoft.Extensions.Options;
using SuntechApp.Bpm;
using System.Text;
using System.Text.Json;

namespace SuntechApp.IpgPlugin
{
    public class EncryptedBPMAttFileService(ILogger<EncryptedBPMAttFileService> logger, IOptions<IpgJsonSettings> IpgJsonSettings, IOptions<RptSettings> rptSettings)
    {
        private readonly ILogger<EncryptedBPMAttFileService> _logger = logger;
        private readonly IpgJsonSettings _IpgJsonSettings = IpgJsonSettings.Value;
        private readonly RptSettings _rptSettings = rptSettings.Value;
        public async Task EncryptFiles()
        {
            try
            {
                using var client = new HttpClient();
                string ContentType = "application/json";
                DateTime DaysAgo = DateTime.Now.AddDays(-31);
                string folderPath = Path.Combine(_rptSettings.BpmAttPath??"", DaysAgo.Year.ToString(), DaysAgo.ToString("MMdd"));
                if (Directory.Exists(folderPath))
                {
                    _logger.LogInformation($"本日加密路径为:{folderPath}");
                }
                else
                {
                    _logger.LogWarning($"本日加密路径为:{folderPath},它并不存在,运行结束");
                    return;
                }
                
                var loginRequest = new IpgJsonLoginRequest
                {
                    Name = _IpgJsonSettings.LoginName,
                    Password = _IpgJsonSettings.Password
                };

                // 序列化请求数据
                string jsonRequest = JsonSerializer.Serialize(loginRequest);

                // 创建请求内容
                var content = new StringContent(
                    jsonRequest,
                    Encoding.UTF8,
                    ContentType
                );

                var response = await client.PostAsync(
                    _IpgJsonSettings.IpgJsonUrl + "login",
                    content
                );

                // 检查响应状态
                if (response.IsSuccessStatusCode)
                {
                    // 读取响应内容
                    string jsonResponse = await response.Content.ReadAsStringAsync();

                    // 反序列化响应数据
                    var Response = JsonSerializer.Deserialize<IpgJsonResponse>(jsonResponse);

                    // 输出返回实例
                    if (Response != null && Response.error == "0")
                    {
                        var param = new IpgJsonEncryptFileRequestParam
                        {
                            Files = []
                        };

                        foreach (string file in Directory.EnumerateFiles(folderPath))
                        {
                            string absolutePath = Path.GetFullPath(file); // 获取全路径
                            param.Files.Add(absolutePath);
                        }
                        _logger.LogInformation($"共找到 {param.Files.Count} 个文件");

                        var EncryptFileRequest = new IpgJsonEncryptFileRequest
                        {
                            LoginID = Response.loginid,
                            Param = param
                        };
                        var Logoutrequst = new IpgJsonLogoutRequest
                        {
                            LoginID = Response.loginid
                        };


                        jsonRequest = JsonSerializer.Serialize(EncryptFileRequest);
                        content = new StringContent(
                            jsonRequest,
                            Encoding.UTF8,
                            ContentType
                        );
                        response = await client.PostAsync(
                            _IpgJsonSettings.IpgJsonUrl + "encryptFile",
                            content
                        );
                        jsonResponse = await response.Content.ReadAsStringAsync();
                        Response = JsonSerializer.Deserialize<IpgJsonResponse>(jsonResponse);
                        if (Response != null && Response.error == "0")
                        {
                            _logger.LogInformation("加密成功");
                        }

                        jsonRequest = JsonSerializer.Serialize(Logoutrequst);
                        content = new StringContent(
                            jsonRequest,
                            Encoding.UTF8,
                            ContentType
                        );
                        response = await client.PostAsync(
                            _IpgJsonSettings.IpgJsonUrl + "logout",
                            content
                        );
                        jsonResponse = await response.Content.ReadAsStringAsync();
                        Response = JsonSerializer.Deserialize<IpgJsonResponse>(jsonResponse);
                        if (Response != null && Response.error == "0")
                        {
                            _logger.LogInformation("注销成功");
                        }
                    }
                }
                else
                {
                    _logger.LogWarning($"请求失败，状态码：{response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"发生异常：{ex.Message}");
            }
        }
        
        
       
    }
}
