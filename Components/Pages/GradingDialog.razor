@using SqlSugar
@using SuntechApp.Data
@inject ISqlSugarClient db
@inject ISnackbar Snackbar
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntime

<MudDialog>
    <DialogContent>
        <div class="d-flex align-center mb-6">
            <MudIcon Icon="@Icons.Material.Filled.Grading" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
            <MudText Typo="Typo.h5" Color="Color.Primary">答案评分</MudText>
        </div>

        @if (Submission != null)
        {
            <!-- 考试信息卡片 -->
            <MudCard Class="mb-4" Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.Assignment" Class="mr-2" />
                            考试信息
                        </MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudButton Variant="Variant.Filled"
                                 Color="Color.Primary"
                                 StartIcon="@Icons.Material.Filled.Download"
                                 OnClick="DownloadAnswer"
                                 Size="Size.Small">
                            下载答案
                        </MudButton>
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Quiz" Size="Size.Small" Class="mr-2" Color="Color.Primary" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">考试名称：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@Submission.ExamTitle</MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="mr-2" Color="Color.Primary" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">学生信息：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@Submission.StudentName (@Submission.StudentNumber)</MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" Class="mr-2" Color="Color.Primary" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">提交时间：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@Submission.SubmittedDate.ToString("yyyy年MM月dd日 HH:mm")</MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.AttachFile" Size="Size.Small" Class="mr-2" Color="Color.Primary" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">答案文件：</MudText>
                            </div>
                            <div class="ml-6">
                                @if (Submission.Files?.Any() == true)
                                {
                                    <MudText Typo="Typo.body1">@Submission.Files.Count 个文件</MudText>
                                    @foreach (var file in Submission.Files.Take(3))
                                    {
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">• @file.FileName</MudText>
                                    }
                                    @if (Submission.Files.Count > 3)
                                    {
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">... 还有 @(Submission.Files.Count - 3) 个文件</MudText>
                                    }
                                }
                                else
                                {
                                    <MudText Typo="Typo.body1" Color="Color.Secondary">无文件</MudText>
                                }
                            </div>
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>

            <!-- 评分表单卡片 -->
            <MudCard Elevation="2" Class="mb-2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.Star" Class="mr-2" />
                            评分详情
                        </MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudForm @ref="gradingForm" @bind-IsValid="@isGradingFormValid">
                        <MudGrid>
                            <MudItem xs="12" md="4">
                                <MudNumericField @bind-Value="gradeScore"
                                               Label="分数"
                                               Min="0"
                                               Max="100"
                                               Step="0.5m"
                                               Required="true"
                                               RequiredError="请输入分数"
                                               Variant="Variant.Outlined"
                                               Adornment="Adornment.End"
                                               AdornmentText="分"
                                               HelperText="请输入0-100之间的分数" />
                            </MudItem>
                            <MudItem xs="12" md="8">
                                <MudTextField @bind-Value="gradeComments"
                                            Label="评语"
                                            Lines="4"
                                            Placeholder="请输入对学生答案的评价和建议..."
                                            Variant="Variant.Outlined"
                                            HelperText="可选：为学生提供详细的反馈" />
                            </MudItem>
                        </MudGrid>
                    </MudForm>
                </MudCardContent>
            </MudCard>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@Cancel"
                 StartIcon="@Icons.Material.Filled.Cancel"
                 Color="Color.Default">
            取消
        </MudButton>
        <MudButton Color="Color.Success"
                 OnClick="@SaveGrading"
                 Disabled="@(!isGradingFormValid || grading)"
                 StartIcon="@Icons.Material.Filled.Save"
                 Variant="Variant.Filled">
            @if (grading)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                <span class="ml-2">保存中...</span>
            }
            else
            {
                <span>保存评分</span>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] IMudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public SuntechApp.Components.Pages.ExamGrading.ExamSubmissionView Submission { get; set; } = null!;
    
    private SqlSugarProvider DefaultDb => db.AsTenant().GetConnection("Default");
    private bool grading = false;
    private bool isGradingFormValid = false;
    private decimal gradeScore = 0;
    private string gradeComments = string.Empty;
    private MudForm gradingForm = null!;

    protected override void OnInitialized()
    {
        if (Submission != null)
        {
            gradeScore = Submission.Score ?? 0;
            gradeComments = Submission.Comments ?? string.Empty;
        }
    }

    private async Task DownloadAnswer()
    {
        if (Submission?.Files?.Any() != true) return;

        try
        {
            foreach (var file in Submission.Files)
            {
                var url = $"/api/ExamFile/download-answer/{Path.GetFileName(file.FilePath)}";
                await JSRuntime.InvokeVoidAsync("open", url, "_blank");
                await Task.Delay(500); // 避免同时下载太多文件
            }
            Snackbar.Add($"开始下载 {Submission.Files.Count} 个文件，请查看浏览器下载内容", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"下载失败: {ex.Message}", Severity.Error);
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task SaveGrading()
    {
        if (Submission == null) return;
        
        try
        {
            grading = true;
            StateHasChanged();

            // 获取当前用户名
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userName = authState.User.Identity?.Name ?? "Unknown";

            // 更新数据库
            await DefaultDb.Updateable<ExamSubmission>()
                .SetColumns(x => new ExamSubmission
                {
                    Score = gradeScore,
                    Comments = gradeComments,
                    GradedBy = userName
                })
                .Where(x => x.Id == Submission.Id)
                .ExecuteCommandAsync();

            Snackbar.Add("评分保存成功！", Severity.Success);
            MudDialog.Close(DialogResult.Ok(true));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            grading = false;
            StateHasChanged();
        }
    }


}
