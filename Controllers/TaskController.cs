using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using SuntechApp.ITS;
using SuntechApp.Bpm;


namespace SuntechApp.Controllers
{
    [Route("api")]  // 路由前缀
    [ApiController]  // 启用模型绑定和JSON格式化
    public class TaskController(IOptions<RptSettings> rptSettings, [FromKeyedServices("BpmService")] IBpmService bpmService,DailySalesReportService dailySalesReportService) : ControllerBase
    {
        private readonly IBpmService _bpmService = bpmService;
        private readonly DailySalesReportService _dailySalesReportService = dailySalesReportService;
        private readonly RptSettings _rptSettings = rptSettings.Value;

        [HttpPost]
        [Route("bpm")]
        public IActionResult Bpm([FromBody] BpmRequest request)
        {
            if (request == null)
                return BadRequest("Invalid request body");

            // 验证Guid匹配
            if (request.LoginID == _rptSettings.LoginID && !string.IsNullOrEmpty(request.TaskName))
            {
                switch (request.TaskName)
                {
                    case "rpt":
                        BackgroundJob.Enqueue(() => _bpmService.RptBpmGenerateFlow(request.TaskID));
                        break;
                    case "cvr":
                        BackgroundJob.Schedule(() => _bpmService.RptBpmCustomerVisitRecord(request.TaskID), TimeSpan.FromMinutes(1));
                        break;
                    case "led":
                        BackgroundJob.Enqueue(() => _bpmService.BpmLedgerModify(request.TaskID));
                        break;
                    case "iiw":
                        BackgroundJob.Schedule(() => _bpmService.BpmInternalWebsite(request.TaskID), TimeSpan.FromSeconds(1));
                        break;
                    case "pri":
                        BackgroundJob.Enqueue(() => _bpmService.PrintWithFont(@"C:\12\阿里云域名续费发票.pdf"));
                        break;
                    case "rwl":
                        BackgroundJob.Schedule(() => _bpmService.BpmRdWorkLog(request.TaskID),TimeSpan.FromSeconds(30));
                        break;
                    case "dsr":
                        BackgroundJob.Enqueue(() => _dailySalesReportService.DailySalesReportSend());
                        break;
                    default:
                        return Ok(new { error = 1, message = "Unknown TaskName" });
                }
                return Ok(new { error = 0 });
            }
            else
            {
                return Ok(new { error = 1, message = "LoginID validation failed" });
            }
        } 
    }
    public class BpmRptRequest
    {
        public Guid LoginID { get; set; }  // Guid类型字段
        public int TaskID { get; set; }    // int类型字段
    }
    public class BpmRequest
    {
        public Guid LoginID { get; set; }  // Guid类型字段
        public int TaskID { get; set; }    // int类型字段
        public string? TaskName { get; set; } // 可选的字符串字段
    }

}
