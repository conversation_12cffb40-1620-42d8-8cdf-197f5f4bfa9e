@using SuntechApp.Data

<MudDialog>
    <DialogContent>
        <MudCard Outlined="true">
            <MudCardContent>
                <MudForm>
                    <MudTextField Label="任务号" @bind-Value="_tasks.TaskID" Disabled="true"></MudTextField>
                    <MudTextField Label="任务名称" @bind-Value="_tasks.TaskName" Required></MudTextField>
                    <MudTextField Label="Cron" @bind-Value="_tasks.Cron" Required></MudTextField>
                </MudForm>
            </MudCardContent>
        </MudCard>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color=Color.Primary Variant="Variant.Filled" OnClick="Submit">确定</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter]
    private IMudDialogInstance MudDialog { get; set; }
    [Parameter]
    public Tasks _tasks { get; set; }

    private void Submit() => MudDialog.Close(DialogResult.Ok(_tasks));

    private void Cancel() => MudDialog.Cancel();
}


