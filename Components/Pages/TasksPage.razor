@page "/TasksPage"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using SuntechApp.Data
@using System.Linq
@inject TasksService TasksService
@inject TaskDetailsService TaskDetailsService
@inject IDialogService DialogService
@inject DialogBase DialogBase

<style>
    .selected {
        background-color: #1E88E5 !important;
    }

        .selected > td {
            color: white !important;
        }

            .selected > td .mud-input {
                color: white !important;
            }
</style>

<MudDataGrid T="Tasks" Items="@tasks" Groupable="false" RowClassFunc="@SelectedRowClassFunc" RowClass="cursor-pointer" Breakpoint="Breakpoint.Sm" SortMode="SortMode.Multiple" Filterable="true" QuickFilter="@_quickFilter" Hover="true" Bordered="true" Dense="true" Virtualize = "true" SelectedItemChanged="@TasksRowClicked">
    <ToolBarContent>
        <MudText Typo="Typo.h6">任务列表</MudText>
        <MudSpacer />
        <MudTextField @bind-Value="_searchString" Placeholder="Search" Adornment="Adornment.Start" Immediate="true"
                      AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
        <MudButton OnClick="@AddTasks" Color="@Color.Success" Class="add-item-btn">增加一行</MudButton>
    </ToolBarContent>
    <Columns>
        <SelectColumn T="Tasks"/>
        <PropertyColumn Property="x => x.TaskID" Title="编号" />
        <PropertyColumn Property="x => x.TaskName" Title="名称" />
        <PropertyColumn Property="x => x.Cron" Title="执行时间(Cron)" />
        <PropertyColumn Property="x => x.RecordDate" Title="更新时间" />
        <TemplateColumn CellClass="d-flex">
            <CellTemplate>
                <MudIconButton Size="@Size.Small" Variant="Variant.Outlined" Color="Color.Primary" Icon="@Icons.Material.Filled.Edit" OnClick="@(() => EditTasks(context))" />
                <MudIconButton Size="@Size.Small" Variant="Variant.Outlined" Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteTask(context))" />

            </CellTemplate>
        </TemplateColumn>
    </Columns>
    <PagerContent>
        <MudDataGridPager PageSizeOptions="new int[]{5, 10, 20, 50, }" />
    </PagerContent>
</MudDataGrid>
<div class="d-flex flex-wrap mt-4">
    <MudText Typo="Typo.h6">任务明细列表</MudText>
    <MudSpacer />
    <MudText Typo="Typo.h6">当前任务号:</MudText>
    <MudText Typo="Typo.h6">@selected_taskID</MudText>
    <MudSpacer />
    <MudButton OnClick="@AddTaskDetails" Color="@Color.Success" Class="add-item-btn">增加一行</MudButton>
</div>
<MudTable Items="@taskDetails" Virtualize="true" HorizontalScrollbar="true" Hover="true" Dense="true" Bordered="true" Filter="@TaskDetailsFiter">
    <HeaderContent>
        <MudTh>编号</MudTh>
        <MudTh>行</MudTh>
        <MudTh>标题</MudTh>
        <MudTh>收件人</MudTh>
        <MudTh>抄送人</MudTh>
        <MudTh>报表名</MudTh>
        <MudTh>扩展</MudTh>
        <MudTh>操作</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="编号">@context.TaskID</MudTd>
        <MudTd DataLabel="行" Style="white-space: nowrap;">@context.seq</MudTd>
        <MudTd DataLabel="标题" Style="white-space: nowrap;">@context.subject</MudTd>
        <MudTd DataLabel="收件人" Style="white-space: nowrap;">@context.toaddress</MudTd>
        <MudTd DataLabel="抄送人" Style="white-space: nowrap;">@context.ccaddress</MudTd>
        <MudTd DataLabel="报表名" Style="white-space: nowrap;">@context.filename</MudTd>
        <MudTd DataLabel="扩展">@context.ext</MudTd>
        <MudTd>
            <MudIconButton Size="@Size.Small" Variant="Variant.Outlined" Color="Color.Primary" Icon="@Icons.Material.Filled.Edit" OnClick="@(() => EditTaskDetails(context))" />
            <MudIconButton Size="@Size.Small" Variant="Variant.Outlined" Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteTaskDetails(context))" />
        </MudTd>
    </RowTemplate>
    <PagerContent>
        <MudTablePager PageSizeOptions="new int[]{5, 10, 20, 50, }" />
    </PagerContent>
    <EditButtonContent Context="button">
        <MudIconButton Size="@Size.Small" Icon="@Icons.Material.Outlined.Edit" Class="pa-0" OnClick="@button.ButtonAction" Disabled="@button.ButtonDisabled" />
    </EditButtonContent>
</MudTable>

@code {
    private List<Tasks> tasks = new List<Tasks>();
    private List<TaskDetails> taskDetails = new List<TaskDetails>();
    private int selected_taskID = 0;
    private string? _searchString;

    protected override async Task OnInitializedAsync()
    {
        tasks = await TasksService.GetTasksAsync();
        taskDetails = await TaskDetailsService.GetTaskDetailsAsync();
    }

    private async Task DeleteTask(CellContext<Tasks> context)
    {
        var dialogReference = await DialogBase.Title("删除确认").ContentText("请确认删除此任务,任务明细将会被一起删除,点击确认后无法恢复").ButtonText("确定").Color(Color.Error).CancleVisible(true).Show();
        var dialogResult = await dialogReference.Result;
        if (!dialogResult!.Canceled)
        {
            await TasksService.DeleteTasksAsync(context.Item.TaskID);
            tasks = await TasksService.GetTasksAsync();
            taskDetails = await TaskDetailsService.GetTaskDetailsAsync();
        }
    }
    private async Task DeleteTaskDetails(TaskDetails context)
    {
        var dialogReference = await DialogBase.Title("删除确认").ContentText("请确认删除此条记录,点击确认后无法恢复").ButtonText("确定").Color(Color.Error).CancleVisible(true).Show();
        var dialogResult = await dialogReference.Result;
        if (!dialogResult!.Canceled)
        {
            await TaskDetailsService.DeleteTaskDetailsAsync(context.id);
            taskDetails = await TaskDetailsService.GetTaskDetailsAsync();
        }
    }
    private async Task AddTasks()
    {
        try
        {
            var parameters = new DialogParameters<_Dialog_TasksEdit>
            {
                { x => x._tasks,new Tasks {TaskName = "新任务", Cron = "0 8 * * *", RecordDate = DateTime.Now }}
            };
            var options = new DialogOptions() { MaxWidth = MaxWidth.ExtraSmall, FullWidth = true };
            var dialogReference = await DialogService.ShowAsync<_Dialog_TasksEdit>("新增", parameters, options);
            var dialogResult = await dialogReference.Result;
            if (!dialogResult!.Canceled)
            {
                await TasksService.UpdateTasksAsync((Tasks)dialogResult.Data!);
                tasks = await TasksService.GetTasksAsync();
            }
        }
        catch (Exception ex)
        {
            var message = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
            await DialogBase.Title("失败").ContentText($"新增失败:{message}").ButtonText("确定").Color(Color.Error).Show();
        }
    }
    async Task AddTaskDetails()
    {
        try
        {
            if (tasks.Any(task => task.TaskID == selected_taskID))
            {
                var parameters = new DialogParameters<_Dialog_TaskDetailsEdit>
                {
                    { x => x._taskDetails, new TaskDetails { TaskID = selected_taskID}}
                };
                var options = new DialogOptions() { MaxWidth = MaxWidth.Medium, FullWidth = true };
                var dialogReference = await DialogService.ShowAsync<_Dialog_TaskDetailsEdit>("新增", parameters, options);
                var dialogResult = await dialogReference.Result;
                if (!dialogResult!.Canceled)
                {
                    await TaskDetailsService.UpdateTaskDetailsAsync((TaskDetails)dialogResult.Data!);
                    taskDetails = await TaskDetailsService.GetTaskDetailsAsync();
                }
            }
            else
            {
                await DialogBase.Title("失败").ContentText("新增失败,当前任务编号在任务列表里必须存在").ButtonText("确定").Color(Color.Error).Show();
            }
        }
        catch (Exception ex)
        {
            var message = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
            await DialogBase.Title("失败").ContentText($"新增失败: {message}").ButtonText("确定").Color(Color.Error).Show();
        }
    }
    async Task EditTasks(CellContext<Tasks> context)
    {
        try
        {
            var parameters = new DialogParameters<_Dialog_TasksEdit>
            {
                { x => x._tasks,context.Item}
            };
            var options = new DialogOptions() { MaxWidth = MaxWidth.ExtraSmall, FullWidth = true };
            var dialogReference = await DialogService.ShowAsync<_Dialog_TasksEdit>("修改", parameters, options);
            var dialogResult = await dialogReference.Result;
            if (!dialogResult!.Canceled)
            {
                await TasksService.UpdateTasksAsync(context.Item);
            }
            tasks = await TasksService.GetTasksAsync();
        }
        catch (Exception ex)
        {
            tasks = await TasksService.GetTasksAsync();
            var message = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
            await DialogBase.Title("失败").ContentText($"修改失败: {message}").ButtonText("确定").Color(Color.Error).Show();
        }
    }
    async Task EditTaskDetails(TaskDetails context)
    {
        try
        {
            var parameters = new DialogParameters<_Dialog_TaskDetailsEdit>
            {
                { x => x._taskDetails, context}
            };
            var options = new DialogOptions() { MaxWidth = MaxWidth.Medium, FullWidth = true };
            var dialogReference = await DialogService.ShowAsync<_Dialog_TaskDetailsEdit>("修改", parameters, options);
            var dialogResult = await dialogReference.Result;
            if (!dialogResult!.Canceled)
            {
                await TaskDetailsService.UpdateTaskDetailsAsync(context);
            }
            taskDetails = await TaskDetailsService.GetTaskDetailsAsync();
        }
        catch (Exception ex)
        {
            taskDetails = await TaskDetailsService.GetTaskDetailsAsync();
            var message = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
            await DialogBase.Title("失败").ContentText($"修改失败: {message}").ButtonText("确定").Color(Color.Error).Show();
        }
    }
    private Func<Tasks, bool> _quickFilter => x =>
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        if ((x.TaskName ?? "").Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if ((x.Cron ?? "").Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if ($"{x.TaskName} {x.Cron}".Contains(_searchString))
            return true;

        return false;
    };
    private Func<TaskDetails, bool> TaskDetailsFiter => x => x.TaskID == selected_taskID;
    private void TasksRowClicked(Tasks tasks)
    {
        if (tasks != null)
        {
            selected_taskID = tasks.TaskID;
            StateHasChanged();
        }
    }
    private Func<Tasks,int,string> SelectedRowClassFunc => (x,y) => selected_taskID != 0 && x.TaskID == selected_taskID ? "selected" : string.Empty;
}
