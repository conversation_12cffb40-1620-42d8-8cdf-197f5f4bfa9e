@page "/TrainingMaterialClassPage"
@using SuntechApp.Data
@using SqlSugar
@using MudBlazor
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@inject ISqlSugarClient db
@inject DialogBase DialogBase
 
    <MudTable Items="@_items"
              Loading="@_loading"
              Hover="true"
              Bordered="true"
              Dense="true"
              Striped="true"
              Filter="_quickFilter"
    >
        <ToolBarContent>
            <MudText Typo="Typo.h6">培训资料分类管理</MudText>
            
                <MudButton OnClick="@OpenAddDialog" Variant="Variant.Filled" Color="@Color.Primary"  Class="add-item-btn ml-10">添加分类</MudButton>
            <MudSpacer/>
            <MudTextField @bind-Value="_searchString"
                          Placeholder="搜索..."
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search"
                          Immediate="true"/>

        </ToolBarContent>

        <HeaderContent>
            <MudTh>
                <MudTableSortLabel SortBy="new Func<TrainingMaterialClass, object>(x=>x.Id)"
                                   InitialDirection="SortDirection.Ascending">
                    ID
                </MudTableSortLabel>
            </MudTh>
            <MudTh>
                <MudTableSortLabel SortBy="new Func<TrainingMaterialClass, object>(x=>x.Class)"
                                   InitialDirection="SortDirection.Ascending">
                
                    分类名称
                </MudTableSortLabel>
                
            </MudTh>
            <MudTh>
                <MudTableSortLabel SortBy="new Func<TrainingMaterialClass, object>(x=>x.ParentId)"
                                   InitialDirection="SortDirection.Ascending">
                    父级分类
                </MudTableSortLabel>
            </MudTh>
            <MudTh>
                <MudTableSortLabel SortBy="new Func<TrainingMaterialClass, object>(x => _allItems.Any(i => i.ParentId == x.Id))"
                                   InitialDirection="SortDirection.Ascending">
                    包含子项
                </MudTableSortLabel>
            </MudTh>
            <MudTh>操作</MudTh>
        </HeaderContent>

        <RowTemplate>
            <MudTd DataLabel="ID">@context.Id</MudTd>
            <MudTd DataLabel="分类名称">@context.Class</MudTd>
            <MudTd DataLabel="父级分类">@(context.ParentId == 0 ? "无" : GetParentName(context.ParentId))</MudTd>
            <MudTd DataLabel="包含子项">
                @{
                    var hasChildren = _allItems.Any(x => x.ParentId == context.Id);
                }
                <MudIcon Icon="@(hasChildren ? Icons.Material.Filled.Check : Icons.Material.Filled.Close)"
                         Color="@(hasChildren ? Color.Success : Color.Error)"/>
            </MudTd>
            <MudTd DataLabel="操作">
                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                               Color="Color.Primary"
                               Size="Size.Small"
                               OnClick="@(() => OpenEditDialog(context))"/>
                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                               Color="Color.Error"
                               Size="Size.Small"
                               OnClick="@(async () => await DeleteItem(context))"/>
            </MudTd>
        </RowTemplate>

        <NoRecordsContent>
            <MudText>没有找到匹配的记录</MudText>
        </NoRecordsContent>
        <PagerContent>
            <MudTablePager PageSizeOptions="new[]{10, 20, 50, }" />
        </PagerContent>
    </MudTable>
  

@code {
    private List<TrainingMaterialClass> _items = [];
    private List<TrainingMaterialClass> _allItems = [];
    private bool _loading;
    private string _searchString = "";
    private SqlSugarProvider DefaultDb => db.AsTenant().GetConnection("Default");

    protected override async Task OnInitializedAsync() {
        await LoadData();
    }

    private async Task LoadData() {
        _loading = true;
        StateHasChanged();

        try {
            _allItems =  await DefaultDb.Queryable<TrainingMaterialClass>().ToListAsync();
            _items = _allItems.ToList();
            FilterItems(_searchString);
        }
        catch (Exception ex) {
            
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
        finally {
            _loading = false;
            StateHasChanged();
        }
    }

    private string GetParentName(int parentId) {
        if (parentId <= 0) return "无";
        var parent = _allItems.FirstOrDefault(x => x.Id == parentId);
        return parent?.Class ?? $"未知分类 (ID: {parentId})";
    }

    private void FilterItems(string searchText) {
        if (string.IsNullOrWhiteSpace(searchText)) {
            _items = _allItems.ToList();
            return;
        }

        _items = _allItems
            .Where(x => x.Class.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        (x.ParentId > 0 && GetParentName(x.ParentId).Contains(searchText, StringComparison.OrdinalIgnoreCase)))
            .ToList();
    }

    // 添加分类
    private async Task OpenAddDialog() {
        var parameters = new DialogParameters {
            { "AllItems", _allItems }
        };

        var dialog = DialogService.Show<EditTrainingMaterialClassDialog>("添加分类", parameters);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is TrainingMaterialClass newItem) {
            try {
                var createdItem = await DefaultDb.Insertable(newItem)
                    .ExecuteReturnEntityAsync();
                
                if (createdItem is not null) {
                    // 如果设置了父分类，更新父分类的 HaveNested 状态
                    if (newItem.ParentId > 0) {
                        await UpdateParentHaveNestedStatus(newItem.ParentId, true);
                    }
                
                    Snackbar.Add("添加成功", Severity.Success);
                    await LoadData(); // 刷新数据
                }
                else {
                    Snackbar.Add("添加失败", Severity.Error);
                }
            }
            catch (Exception ex) {
                Snackbar.Add($"添加失败: {ex.Message}", Severity.Error);
            }
        }
    }
    private async Task UpdateParentHaveNestedStatus(int parentId, bool hasNested) {
        try {
            var childCount = await DefaultDb.Queryable<TrainingMaterialClass>()
                .Where(x => x.ParentId == parentId)
                .CountAsync();
            bool newHaveNestedValue = hasNested || childCount > 0;
            await DefaultDb.Updateable<TrainingMaterialClass>()
                .SetColumns(x => x.HaveNested == newHaveNestedValue)
                .Where(x => x.Id == parentId)
                .ExecuteCommandAsync();
        }
        catch (Exception ex) {
            Snackbar.Add($"更新父分类状态失败: {ex.Message}", Severity.Error);
        }
    }
    // 编辑分类
    private async Task OpenEditDialog(TrainingMaterialClass item) {
        var parameters = new DialogParameters {
            { "Item", item },
            { "AllItems", _allItems.Where(x => x.Id != item.Id).ToList() } // 排除自身
        };

        var dialog = await DialogService.ShowAsync<EditTrainingMaterialClassDialog>("编辑分类", parameters);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is TrainingMaterialClass updatedItem) {
            try {
                // 检查父分类是否改变
                bool parentChanged = item.ParentId != updatedItem.ParentId;
                int oldParentId = item.ParentId;
            
                var success = await DefaultDb.Updateable(updatedItem)
                    .UpdateColumns(x => new { x.Class, x.ParentId, x.HaveNested })
                    .ExecuteCommandHasChangeAsync();
                
                if (success) {
                    // 如果父分类有变化，更新新旧父分类的 HaveNested 状态
                    if (parentChanged) {
                        // 更新旧父分类的 HaveNested 状态
                        if (oldParentId > 0) {
                            await UpdateParentHaveNestedStatus(oldParentId, false);
                        }
                    
                        // 更新新父分类的 HaveNested 状态
                        if (updatedItem.ParentId > 0) {
                            await UpdateParentHaveNestedStatus(updatedItem.ParentId, true);
                        }
                    }
                
                    Snackbar.Add("更新成功", Severity.Success);
                    await LoadData(); // 刷新数据
                }
                else {
                    Snackbar.Add("更新失败", Severity.Error);
                }
            }
            catch (Exception ex) {
                Snackbar.Add($"更新失败: {ex.Message}", Severity.Error);
            }
        }
    }

    // 删除分类
    private async Task DeleteItem(TrainingMaterialClass item) {

        var dialogReference = await DialogBase.Title("删除确认").ContentText("请确认删除此记录,点击确认后无法恢复").ButtonText("确定").Color(Color.Error).CancleVisible(true).Show();
        var dialogResult = await dialogReference.Result;
        if (!dialogResult!.Canceled) {
            try {
                int parentId = item.ParentId;
                var success = await DefaultDb.Deleteable<TrainingMaterialClass>()
                    .Where(x => x.Id == item.Id)
                    .ExecuteCommandHasChangeAsync();
                
                if (success) {
                    // 如果有关联的父分类，更新其 HaveNested 状态
                    if (parentId > 0) {
                        await UpdateParentHaveNestedStatus(parentId, false);
                    }
                
                    Snackbar.Add("删除成功", Severity.Success);
                    await LoadData(); // 刷新数据
                }
                else {
                    Snackbar.Add("删除失败", Severity.Error);
                }
            }
            catch (Exception ex) {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }


    private Func<TrainingMaterialClass, bool> _quickFilter => x => {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        // 搜索分类名称
        if ((x.Class ?? "").Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        // 搜索父级分类名称（通过ID查找）

        // 搜索ID（转换为字符串比较）
        if (x.Id.ToString().Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        // 组合搜索（ID + 分类名称）
        if ($"{x.Id} {x.Class}".Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        return false;
    };

}