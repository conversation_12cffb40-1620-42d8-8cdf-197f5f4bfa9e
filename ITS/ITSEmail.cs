using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;
using System.Text.RegularExpressions;

namespace SuntechApp.ITS
{
    public class ITSEmailServices(ILogger<ITSEmailServices> logger, IOptions<ITSEmailSettings> iTSEmailSettings)
    {
        private readonly ITSEmailSettings _iTSEmailSettings = iTSEmailSettings.Value;
        private readonly ILogger<ITSEmailServices> _logger = logger;
        private string _subject = "";
        private string _body = "";
        private List<string> _toAddresses = [];
        private List<string> _ccAddresses = [];
        private List<string> _bccAddresses = [];
        private List<ITSEmailAttachment> _attachment = [];


        public ITSEmailServices Subject(string? subject)
        {
            _subject = subject ?? string.Empty;
            return this;
        }
        public ITSEmailServices Body(string? body)
        {
            _body = body ?? string.Empty;
            return this;
        }
        public ITSEmailServices Attach(List<ITSEmailAttachment>? attach)
        {
            _attachment = attach ?? [];
            return this;
        }
        public ITSEmailServices ToAddress(string? toAddress)
        {
            _toAddresses = [];
            if (!string.IsNullOrEmpty(toAddress))
            {
                List<string> AddressList = [.. toAddress.Split([';', ',', '，', '；'], StringSplitOptions.RemoveEmptyEntries)];
                foreach (var address in AddressList)
                {
                    string formartAddress = FormatAddress(address);
                    if (!string.IsNullOrEmpty(formartAddress))
                        _toAddresses.Add(formartAddress);
                }
            }
            return this;
        }
        public ITSEmailServices CcAddress(string? ccAddress)
        {
            _ccAddresses = [];
            if (!string.IsNullOrEmpty(ccAddress))
            {
                List<string> AddressList = [.. ccAddress.Split([';', ',', '，', '；'], StringSplitOptions.RemoveEmptyEntries)];
                foreach (var address in AddressList)
                {
                    string formartAddress = FormatAddress(address);
                    if (!string.IsNullOrEmpty(formartAddress))
                        _ccAddresses.Add(formartAddress);
                }
            }
            return this;
        }
        public ITSEmailServices BccAddress(string? bccAddress)
        {
            _bccAddresses = [];
            if (!string.IsNullOrEmpty(bccAddress))
            {
                List<string> AddressList = [.. bccAddress.Split([';', ',', '，', '；'], StringSplitOptions.RemoveEmptyEntries)];
                foreach (var address in AddressList)
                {
                    string formartAddress = FormatAddress(address);
                    if (!string.IsNullOrEmpty(formartAddress))
                        _bccAddresses.Add(formartAddress);
                }
            }
            return this;
        }

        public async Task SendAsync()
        {
            int maxRetries = Convert.ToInt32(_iTSEmailSettings.MaxRetries);
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    using var message = new MimeMessage();
                    message.From.Add(new MailboxAddress(_iTSEmailSettings.EmailAccountName, _iTSEmailSettings.EmailAccount));
                    foreach (var toAddress in _toAddresses)
                    {
                        message.To.Add(new MailboxAddress("", toAddress));
                    }
                    foreach (var ccAddress in _ccAddresses)
                    {
                        message.Cc.Add(new MailboxAddress("", ccAddress));
                    }
                    foreach (var bccAddress in _bccAddresses)
                    {
                        message.Bcc.Add(new MailboxAddress("", bccAddress));
                    }
                    var bodyBuilder = new BodyBuilder
                    {
                        HtmlBody = _body,
                    };
                    foreach (var attachment in _attachment)
                    {
                        if (File.Exists(attachment.AttachmentPath))
                        {
                            var ext = Path.GetExtension(attachment.AttachmentPath).ToLowerInvariant();
                            var fileName = attachment.FileName + ext;
                            var contentType = ext switch
                            {
                                // Office
                                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                ".xls"  => "application/vnd.ms-excel",
                                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                                ".doc"  => "application/msword",
                                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                                ".ppt"  => "application/vnd.ms-powerpoint",
                                // PDF
                                ".pdf"  => "application/pdf",
                                // 图片
                                ".jpg" or ".jpeg" => "image/jpeg",
                                ".png" => "image/png",
                                ".gif" => "image/gif",
                                ".bmp" => "image/bmp",
                                ".tif" or ".tiff" => "image/tiff",
                                ".svg" => "image/svg+xml",
                                // 压缩包
                                ".zip" => "application/zip",
                                ".rar" => "application/vnd.rar",
                                ".7z"  => "application/x-7z-compressed",
                                ".tar" => "application/x-tar",
                                ".gz"  => "application/gzip",
                                // 文本
                                ".txt" => "text/plain",
                                ".csv" => "text/csv",
                                ".xml" => "application/xml",
                                ".json" => "application/json",
                                ".html" => "text/html",
                                ".htm" => "text/html",
                                // 音频
                                ".mp3" => "audio/mpeg",
                                ".wav" => "audio/wav",
                                ".ogg" => "audio/ogg",
                                // 视频
                                ".mp4" => "video/mp4",
                                ".avi" => "video/x-msvideo",
                                ".mov" => "video/quicktime",
                                ".wmv" => "video/x-ms-wmv",
                                ".flv" => "video/x-flv",
                                ".mkv" => "video/x-matroska",
                                // 其他
                                _ => "application/octet-stream"
                            };

                            var mimePart = new MimePart(contentType)
                            {
                                Content = new MimeContent(File.OpenRead(attachment.AttachmentPath)),
                                ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                                ContentTransferEncoding = ContentEncoding.Base64,
                                FileName = fileName
                            };
                            bodyBuilder.Attachments.Add(mimePart);
                        }
                    }
                    message.Body = bodyBuilder.ToMessageBody();
                    message.Subject = _subject;
                    using var client = new SmtpClient();
                    await client.ConnectAsync(_iTSEmailSettings.SmtpServer, 25, SecureSocketOptions.None);
                    await client.AuthenticateAsync(_iTSEmailSettings.EmailAccount, _iTSEmailSettings.EmailPassword);
                    await client.SendAsync(message);
                    await client.DisconnectAsync(true);
                    break;
                }
                catch (Exception ex)
                {
                    if (attempt < maxRetries)
                    {
                        // 记录重试信息（当前尝试次数和剩余次数）
                        _logger.LogError("邮件发送失败(第 {CurrentAttempt} 次尝试，剩余 {RemainingAttempts} 次): {Exception}", attempt, maxRetries - attempt, ex.ToString());
                        if (attempt == 1)
                        {
                            await Task.Delay(Convert.ToInt32(_iTSEmailSettings.FirstDelaySecond) * 1000);
                        }
                        else
                        {
                            await Task.Delay(Convert.ToInt32(_iTSEmailSettings.DelayMinute) * 60 * 1000); // 等待2分钟
                        }
                    }
                    else
                    {
                        // 所有重试均失败时记录最终错误
                        _logger.LogError("邮件发送失败(已用尽 {MaxRetries} 次尝试): {Exception}", maxRetries, ex.ToString()
                        );
                        // throw;
                    }
                }
            }
        }
        private string FormatAddress(string address)
        {
            if (string.IsNullOrEmpty(address))
            {
                return string.Empty;
            }
            if (Regex.IsMatch(address, @"^[a-zA-Z0-9!# $ %&'*+/=?^_`{|}～-]+(?:\.[a-zA-Z0-9!# $ %&'*+/=?^_`{|}～-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9]{2,}$"))
            {
                return address; // 是有效邮件地址，直接返回
            }
            if (Regex.IsMatch(address + _iTSEmailSettings.DefaultSuffix, @"^[a-zA-Z0-9!# $ %&'*+/=?^_`{|}～-]+(?:\.[a-zA-Z0-9!# $ %&'*+/=?^_`{|}～-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9]{2,}$"))
            {
                return address + _iTSEmailSettings.DefaultSuffix;// 补全域名
            }
            return string.Empty;
        }
        
    }
}
