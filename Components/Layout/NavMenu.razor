@implements IDisposable

@inject NavigationManager NavigationManager

<MudNavMenu Bordered="true">
    <MudNavLink Href="" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Home">主页</MudNavLink>
    <MudNavLink Href="Materials" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Book">培训资料</MudNavLink>
    <MudNavLink Href="Exam" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Quiz">在线考试</MudNavLink>
    <MudNavGroup Title="常用工具" Icon="@Icons.Material.Filled.List" Expanded="false">
        <MudNavLink Href="filemerge" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">文件转PDF</MudNavLink>
    </MudNavGroup>
    <AuthorizeView>
        <Authorized>
            <MudNavGroup Title="功能测试" Icon="@Icons.Material.Filled.List" Expanded="false">
                <MudNavLink Href="counter" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Add">计数器</MudNavLink>
                <MudNavLink Href="weather" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">天气</MudNavLink>
                <MudNavLink Href="EmployeeR" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">表格雇员</MudNavLink>
                <MudNavLink Href="EmployeeRC" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">框架雇员</MudNavLink>
            </MudNavGroup>  
            <MudNavGroup Title="功能设置" Icon="@Icons.Material.Filled.List" Expanded="false">
                <MudNavLink Href="TasksPage" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">报表自动发送</MudNavLink>
                <MudNavLink Href="AddBackgroundJobPage" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">后台任务新增</MudNavLink>
                <MudNavLink Href="InternalTrxCustomersPage" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">内部交易客户</MudNavLink>
                <MudNavLink Href="TrainingMaterialClassPage" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">培训资料库目录设置</MudNavLink>
            </MudNavGroup>
            <MudNavGroup Title="考试管理" Icon="@Icons.Material.Filled.School" Expanded="false">
                <MudNavLink Href="ExamManagement" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.UploadFile">试卷管理</MudNavLink>
                <MudNavLink Href="ExamGrading" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Grading">评卷管理</MudNavLink>
                <MudNavLink Href="ExamStatistics" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Analytics">考试统计</MudNavLink>
            </MudNavGroup>
            <MudNavLink Href="hangfire" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Dashboard">后台任务</MudNavLink>
        </Authorized>
    </AuthorizeView>
    @* <MudNavLink Href="auth" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Lock">Auth Required</MudNavLink> *@
    <AuthorizeView>
        <Authorized>
            <MudNavLink Href="Account/Manage" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Person">@context.User.Identity?.Name</MudNavLink>
            <form action="Account/Logout" method="post">
                <AntiforgeryToken />
                <input type="hidden" name="ReturnUrl" value="@currentUrl" />
                <button type="submit" class="mud-nav-link mud-ripple">
                    <MudIcon Icon="@Icons.Material.Filled.Logout"   Color="Color.Info" Class="mr-3"></MudIcon> Logout
                </button>
            </form>
        </Authorized>
        <NotAuthorized>
            @* <MudNavLink Href="Account/Register" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Person">Register</MudNavLink> *@
            <MudNavLink Href="Account/Login" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Password">管理员登录</MudNavLink>
        </NotAuthorized>
    </AuthorizeView>
</MudNavMenu>


@code {
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}

