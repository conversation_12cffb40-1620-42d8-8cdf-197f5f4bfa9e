@page "/InternalTrxCustomersPage"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using SuntechApp.Data
@using System.Linq
@inject IRptRepository<InternalTrxCustomers,int> RptRepository
@inject IDialogService DialogService
@inject DialogBase DialogBase
@inject ValueDataMap ValueDataMap

<MudDataGrid T="InternalTrxCustomers" Items="@internalTrxCustomers" Groupable="false" RowClass="cursor-pointer" Breakpoint="Breakpoint.Sm" SortMode="SortMode.Multiple" Filterable="true" QuickFilter="@_quickFilter" Hover="true" Bordered="true" Dense="true" Virtualize="true" ShowColumnOptions="true" ColumnResizeMode="ResizeMode.Container" HorizontalScrollbar = "true">
    <ToolBarContent>
        <MudText Typo="Typo.h6">内部交易客户</MudText>
        <MudSpacer />
        <MudTextField @bind-Value="_searchString" Placeholder="Search" Adornment="Adornment.Start" Immediate="true"
                      AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
        <MudButton OnClick="@AddData" Color="@Color.Success" Class="add-item-btn">增加一行</MudButton>
    </ToolBarContent>
    <Columns>
        <SelectColumn T="InternalTrxCustomers" />
        <PropertyColumn Property="x => x.CustNum" Title="客户编号" CellStyle="text-align: center" />
        <PropertyColumn Property="x => x.Name" Title="客户名称" CellStyle="text-align: center" />
        <PropertyColumn Property="x => x.Type" Title="类型" />
        <PropertyColumn Property="x => x.RecordDate" Title="更新时间" Format="yyyy-MM-dd hh:mm"/>
        <TemplateColumn CellClass="d-flex" Title="操作">
            <CellTemplate>
                <MudIconButton Size="@Size.Small" Variant="Variant.Outlined" Color="Color.Primary" Icon="@Icons.Material.Filled.Edit" OnClick="@(() => EditData(context))" />
                <MudIconButton Size="@Size.Small" Variant="Variant.Outlined" Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteData(context))" />
            </CellTemplate>
        </TemplateColumn>
    </Columns>
    <PagerContent>
        <MudDataGridPager PageSizeOptions="new int[]{10, 20, 50, }" />
    </PagerContent>
</MudDataGrid>


@code {
    private List<InternalTrxCustomers> internalTrxCustomers = new List<InternalTrxCustomers>();
    private readonly List<InternalTrxCustomersType> _internalTrxCustomersType = ValueDataMap.InternalTrxCustomersTypeMap();
    private Dictionary<string, string>? TypeMap { get; set; }
    private string? _searchString;
    private string? _defaut = "";

    
    protected override async Task OnInitializedAsync()
    {
        internalTrxCustomers = await RptRepository.GetDataAsync();
        TypeMap = _internalTrxCustomersType
           .ToDictionary(item => item.Type, item => item.Display);
    }
    
    private async Task DeleteData(CellContext<InternalTrxCustomers> context)
    {
        if (context is null)
        {
            throw new ArgumentNullException(nameof(context));
        }
        var dialogReference = await DialogBase.Title("删除确认").ContentText("请确认删除此记录,点击确认后无法恢复").ButtonText("确定").Color(Color.Error).CancleVisible(true).Show();
        var dialogResult = await dialogReference.Result;
        if (!dialogResult!.Canceled)
        {
            await RptRepository.DeleteAsync(context.Item.Id);
            internalTrxCustomers = await RptRepository.GetDataAsync();
        }
    }
    private async Task AddData()
    {
        try
        {
            var parameters = new DialogParameters<_Dialog_InternalTrxCustomersEdit>
            {
                { x => x._internalTrxCustomers ,new InternalTrxCustomers {CreateDate = DateTime.Now}}
            };
            var options = new DialogOptions() { MaxWidth = MaxWidth.Small, FullWidth = true };
            var dialogReference = await DialogService.ShowAsync<_Dialog_InternalTrxCustomersEdit>("新增", parameters, options);
            var dialogResult = await dialogReference.Result;
            if (!dialogResult!.Canceled)
            {
                await RptRepository.UpdateDataAsync((InternalTrxCustomers)dialogResult.Data!);
                internalTrxCustomers = await RptRepository.GetDataAsync();
            }
        }
        catch (Exception ex)
        {
            var message = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
            await DialogBase.Title("失败").ContentText($"新增失败:{message}").ButtonText("确定").Color(Color.Error).Show();
        }
    }
    
    async Task EditData(CellContext<InternalTrxCustomers> context)
    {
        try
        {
            var parameters = new DialogParameters<_Dialog_InternalTrxCustomersEdit>
                 {
                     { x => x._internalTrxCustomers,context.Item}
                 };
            var options = new DialogOptions() { MaxWidth = MaxWidth.ExtraSmall, FullWidth = true };
            var dialogReference = await DialogService.ShowAsync<_Dialog_InternalTrxCustomersEdit>("修改", parameters, options);
            var dialogResult = await dialogReference.Result;
            if (!dialogResult!.Canceled)
            {
                await RptRepository.UpdateDataAsync(context.Item);
            }
            internalTrxCustomers = await RptRepository.GetDataAsync();
        }
        catch (Exception ex)
        {
            internalTrxCustomers = await RptRepository.GetDataAsync();
            var message = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
            await DialogBase.Title("失败").ContentText($"修改失败: {message}").ButtonText("确定").Color(Color.Error).Show();
        }
    }
    private Func<InternalTrxCustomers, bool> _quickFilter => x =>
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;
        if ((x.Name ?? "").Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;
        if ((x.CustNum ?? "").Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;
        if ($"{x.CustNum} {x.Name}".Contains(_searchString))
            return true;
        return false;
    }; 
}

