@page "/Materials"
@page "/Materials/{category:int}"
@using SuntechApp.Data
@using SqlSugar
@inject ISqlSugarClient db
@inject NavigationManager Navigation

<style>
    .breadcrumbs-small {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        min-height: 16px !important;
        font-size: 0.85rem !important;
        line-height: 1.2 !important;
    }

        .breadcrumbs-small .mud-breadcrumbs-item {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            min-height: 0 !important;
            font-size: 0.85rem !important;
            line-height: 1.2 !important;
        }

        .breadcrumbs-small .mud-link {
            padding: 0 2px !important;
            font-size: 0.85rem !important;
            line-height: 1.2 !important;
        }

    .th-bold {
        font-weight: bold !important;
    }
</style>

<MudBreadcrumbs Items="_items" Separator=">" Class="breadcrumbs-small">
    <ItemTemplate Context="item">
        <MudLink Href="@item.Href">@item.Text</MudLink>
    </ItemTemplate>
</MudBreadcrumbs>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="py-4">
    <MudGrid>
        <MudItem xs="12" sm="3" md="2">
            <MudPaper Class="pa-4" Style="height:100%;">
                <MudList T="string"
                         SelectionMode="SelectionMode.SingleSelection"
                         ReadOnly="false"
                         Color="@Color.Info"
                         SelectedValue="@_selectedCategoryId?.ToString()"
                         SelectedValueChanged="@OnSelectedValueChanged">
                    @foreach (var trainingMaterialClass in TrainingMaterialClass) {
                        @if (trainingMaterialClass.HaveNested == false && trainingMaterialClass.ParentId == 0) {
                            <MudListItem Text="@trainingMaterialClass.Class"
                                         Value="@trainingMaterialClass.Id.ToString()"/>
                        }
                        else if (trainingMaterialClass.HaveNested == true && trainingMaterialClass.ParentId == 0) {
                            <MudListItem Text="@trainingMaterialClass.Class"
                                         Value="@trainingMaterialClass.Id.ToString()">
                                <NestedList>
                                    @foreach (var nestedItem in TrainingMaterialClass.Where(x => x.ParentId == trainingMaterialClass.Id)) {
                                        <MudListItem Text="@nestedItem.Class" Value="@nestedItem.Id.ToString()"/>
                                    }
                                </NestedList>
                            </MudListItem>
                        }
                    }
                </MudList>
            </MudPaper>
        </MudItem>
        <MudItem xs="12" sm="8" md="10">

            <MudTable Items="TrainingMaterial" Hover="true" Dense="true" Filter="@FilterFunc">
                <ToolBarContent>
                    <MudText Typo="Typo.h6" Class="th-bold">资料列表</MudText>
                    <MudSpacer/>
                    <MudTextField @bind-Value="searchString1" Placeholder="搜索..." Adornment="Adornment.Start"
                                  AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"
                                  Class="mt-0"></MudTextField>
                </ToolBarContent>
                <HeaderContent>
                    <MudTh Class="th-bold">标题</MudTh>
                    <MudTh Class="th-bold">上传者</MudTh>
                    <MudTh Class="th-bold">更新日期</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>
                        <MudLink Href="@($"MaterialsPage/{context.Id}")">@context.Title</MudLink>
                    </MudTd>
                    <MudTd>@context.Author</MudTd>
                    <MudTd>@context.ModifiedDate?.ToString("yyyy-MM-dd")</MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager PageSizeOptions="new int[] { 12, 50, 100 }"/>
                </PagerContent>
            </MudTable>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    [Parameter] public int? category { get; set; }

    public SelectionMode SelectionMode = SelectionMode.SingleSelection;
    private int? _selectedCategoryId;
    private List<TrainingMaterial> TrainingMaterial = [];
    private List<TrainingMaterial> allTrainingMaterial = [];
    private List<TrainingMaterialClass> TrainingMaterialClass = [];
    private string searchString1 = "";

    private List<BreadcrumbItem> _items = [
        new BreadcrumbItem("首页", "/"),
        new BreadcrumbItem("培训资料", "/Materials")
    ];


    protected override async Task OnInitializedAsync() {
        TrainingMaterialClass = await db.AsTenant().QueryableWithAttr<TrainingMaterialClass>()
            .OrderBy(n => n.Id, OrderByType.Asc)
            .ToListAsync();
        allTrainingMaterial = await db.AsTenant().QueryableWithAttr<TrainingMaterial>()
            .OrderBy(n => n.ModifiedDate, OrderByType.Desc)
            .Take(3000)
            .ToListAsync();
        TrainingMaterial = allTrainingMaterial;
        UpdateBreadcrumbs();
    }

    protected override async Task OnParametersSetAsync() {
        if (category.HasValue) {
            _selectedCategoryId = category;
            var selectedClass = TrainingMaterialClass.FirstOrDefault(x => x.Id == category);
            TrainingMaterial = allTrainingMaterial.Where(x => x.ParentId == selectedClass?.Id).ToList();
            if (selectedClass != null) {
                UpdateBreadcrumbs(selectedClass);
            }
        }
        else {
            TrainingMaterial = allTrainingMaterial;
            UpdateBreadcrumbs();
        }

        StateHasChanged();
    }

    private void UpdateBreadcrumbs(TrainingMaterialClass? selectedClass = null) {
        _items = new List<BreadcrumbItem> {
            new BreadcrumbItem("首页", "/"),
            new BreadcrumbItem("培训资料", "/Materials")
        };

        if (selectedClass is not null) {
            // 如果有父级分类，添加到面包屑
            if (selectedClass.ParentId > 0) {
                var parent = TrainingMaterialClass.FirstOrDefault(x => x.Id == selectedClass.ParentId);
                if (parent is not null) {
                    _items.Add(new BreadcrumbItem(parent.Class, $"/Materials/{parent.Id}", disabled: true));
                }
            }

            _items.Add(new BreadcrumbItem(selectedClass.Class, $"/Materials/{selectedClass.Id}"));
        }
    }
    private void OnSelectedValueChanged(string? value) {
        if (int.TryParse(value, out var parentId)) {
            _selectedCategoryId = parentId;
            var selectedClass = TrainingMaterialClass.FirstOrDefault(x => x.Id == parentId);
            TrainingMaterial = allTrainingMaterial.Where(x => x.ParentId == parentId).ToList();
            UpdateBreadcrumbs(selectedClass);
            // 更新 URL
            Navigation.NavigateTo($"/Materials/{parentId}");
        }
        else {
            _selectedCategoryId = null;
            TrainingMaterial = allTrainingMaterial;
            UpdateBreadcrumbs();
            // 重置 URL
            Navigation.NavigateTo("/Materials");
        }
    }

    private bool FilterFunc(TrainingMaterial trainingMaterial) {
        if (string.IsNullOrWhiteSpace(searchString1))
            return true;
        if (trainingMaterial.Title.Contains(searchString1, StringComparison.OrdinalIgnoreCase))
            return true;
        if (trainingMaterial.Slug.Contains(searchString1, StringComparison.OrdinalIgnoreCase))
            return true;
        return false;
    }

}