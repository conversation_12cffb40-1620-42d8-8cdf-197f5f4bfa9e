@page "/filemerge"
@using System.IO
@using System.Diagnostics
@using Dm.util
@using PdfSharpCore.Pdf
@using PdfSharpCore.Pdf.IO
@using Microsoft.AspNetCore.Hosting
@using Microsoft.Extensions.Options
@using SuntechApp.IpgPlugin
@using SuntechApp.Bpm
@inject IJSRuntime JSRuntime
@inject ISnackbar Snackbar
@inject IWebHostEnvironment Env
@inject IOptions<IntranetResourceSite> IntranetResourceSite
@inject HttpClient Client
@inject DecryptFilesService DecryptFilesService
@inject EncryptFilesService EncryptFilesService
@rendermode InteractiveServer


<PageTitle>PDF 合并工具</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-6">
    <MudPaper Class="pa-6" Elevation="3">
        <MudText Typo="Typo.h5" Class="mb-4">PDF 合并工具</MudText>

        <!-- 上传状态提示 -->
        @if (_isUploading) {
            <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mb-4"/>
            <MudText Class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.CloudUpload" Class="mr-2"/>
                正在上传文件...
            </MudText>
        }

        <MudForm @ref="_form" @onsubmit:preventDefault="true">
            <!-- 文件上传区域 -->
            <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                           FilesChanged="@OnFilesChanged"
                           Accept=".pdf,.txt,.doc,.docx,.odt, .odp,.ods, .xls, .xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.gif,.bmp,.tif,.tiff,.epub"
                           Disabled="@(_isProcessing || _isUploading)"
                           Class="mb-4">
                <ActivatorContent>
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.CloudUpload"
                               Disabled="@(_isProcessing || _isUploading)">
                        上传文件
                    </MudButton>
                </ActivatorContent>
            </MudFileUpload>

            <!-- 在文件上传区域下方添加这个提示 -->
            <MudAlert Severity="Severity.Info" Class="mb-4" Dense Elevation="0">
                <MudText Class="d-flex align-center">
                    <div>
                        <strong>上传提示</strong>
                        <ul class="ma-0 pl-4">
                            <li>上传大文件时请耐心等待，处理完成后会自动显示</li>
                            <li>支持的文件格式：PDF、Word、Excel、PPT、图片、Epub等</li>
                            <li>单个文件大小不超过50MB</li>
                        </ul>
                    </div>
                </MudText>
            </MudAlert>

            <!-- 转换和合并进度 -->
            @if (_isProcessing) {
                <MudPaper Class="pa-3 mb-4" Elevation="0" Style="border: 1px dashed">
                    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="my-7"/>
                    <MudText Class="text-center d-block">
                        @if (_currentProcessingFile != null) {
                            <div>正在处理: @_currentProcessingFile</div>
                        }
                        <div>已完成: @_processedFiles / @_totalFiles</div>
                    </MudText>
                    @if (!string.IsNullOrEmpty(_currentStatus)) {
                        <MudText Class="text-center d-block mt-2" Color="Color.Primary">
                            @_currentStatus
                        </MudText>
                    }
                </MudPaper>
            }

            <!-- 已上传文件列表 -->
            @if (_uploadedFiles.Any()) {
                <MudPaper Class="pa-3 mb-4" Elevation="0" Style="border: 1px solid; border-radius: 4px;">
                    <MudList T="string" Dense="true">
                        @foreach (var file in _uploadedFiles) {
                            <MudListItem T="string">
                                <MudIcon Icon="@GetFileIcon(file.Name)" Class="mr-2"/>
                                <MudText>@file.Name (@(file.Size / 1024) KB)</MudText>
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                               OnClick="@(() => RemoveFile(file))"
                                               Color="Color.Error"
                                               Class="ml-auto"
                                               Size="Size.Small"
                                               Disabled="@(_isProcessing || _isUploading)"/>
                            </MudListItem>
                        }
                    </MudList>
                </MudPaper>
            }

            <!-- 操作按钮 -->
            <div class="d-flex justify-end">
                <MudButton Variant="Variant.Outlined"
                           Color="Color.Error"
                           OnClick="@ClearAll"
                           Disabled="@(!_uploadedFiles.Any() || _isProcessing || _isUploading)"
                           Class="mr-2">
                    清空
                </MudButton>
                @if (!string.IsNullOrEmpty(_downloadUrl)) {
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Success"
                               StartIcon="@Icons.Material.Filled.Download"
                               OnClick="@DownloadFile"
                               Class="mr-2">
                        下载合并后的文件
                    </MudButton>
                }
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="@MergeFiles"
                           Disabled="@(_isProcessing || _isUploading)">
                    <MudIcon Icon="@Icons.Material.Filled.Merge" Class="mr-2"/>
                    @(string.IsNullOrEmpty(_downloadUrl) ? "合并为PDF" : "重新合并")
                </MudButton>
            </div>
        </MudForm>
    </MudPaper>
</MudContainer>


@code {
    private MudForm _form;
    private readonly List<IBrowserFile> _uploadedFiles = [];
    private bool _isProcessing;
    private bool _isUploading;
    private int _processedFiles;
    private int _totalFiles;
    private string? _currentProcessingFile;
    private string _currentStatus = "";
    private string _downloadUrl = string.Empty;


    private Task OnFilesChanged(IReadOnlyList<IBrowserFile>? files) {
        if (files is null || !files.Any()) return Task.CompletedTask;
        _isUploading = true;
        StateHasChanged();

        var validFiles = new List<IBrowserFile>();
        var uploadedCount = 0;
        try {
            // 显示上传进度
            _currentStatus = "正在检查文件...";
            StateHasChanged();
            foreach (var file in files) {
                _currentProcessingFile = file.Name;
                StateHasChanged();
                // 检查文件大小限制（例如 50MB）
                if (file.Size > 50 * 1024 * 1024) {
                    Snackbar.Add($"文件 {file.Name} 超过50MB限制", Severity.Warning);
                    continue;
                }

                // 检查文件类型
                var ext = Path.GetExtension(file.Name).ToLowerInvariant();
                if (!IsSupportedFileType(ext)) {
                    Snackbar.Add($"不支持的文件类型: {ext}", Severity.Warning);
                    continue;
                }

                validFiles.Add(file);
                uploadedCount++;
                _currentStatus = $"已检查 {uploadedCount}/{files.Count} 个文件";
                StateHasChanged();
            }

            if (validFiles.Any()) {
                _uploadedFiles.AddRange(validFiles);
                Snackbar.Add($"已添加 {validFiles.Count} 个文件", Severity.Success);
            }
        }
        catch (Exception ex) {
            Snackbar.Add($"处理文件时出错: {ex.Message}", Severity.Error);
        }
        finally {
            _isUploading = false;
            _currentProcessingFile = null;
            _currentStatus = "";
            StateHasChanged();
        }

        return Task.CompletedTask;
    }

    private bool IsSupportedFileType(string extension) {
        string[] supportedTypes = [
            ".pdf", ".txt", ".doc", ".docx", ".odt", ".odp",
            ".ods", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg",
            ".png", ".gif", ".bmp", ".tif", ".tiff", ".epub"
        ];
        return supportedTypes.Contains(extension);
    }

    private string GetFileIcon(string fileName) {
        var ext = Path.GetExtension(fileName).ToLowerInvariant();
        return ext switch {
            ".pdf" => Icons.Material.Filled.PictureAsPdf,
            ".doc" or ".docx" or ".odt" => Icons.Material.Filled.Description,
            ".xls" or ".xlsx" => Icons.Material.Filled.TableChart,
            ".ppt" or ".pptx" => Icons.Material.Filled.Slideshow,
            ".txt" => Icons.Material.Filled.TextFields,
            ".jpeg" or ".jpg" or ".png" or ".gif" or ".bmp" or ".tif" or ".tiff" => Icons.Material.Filled.Image,
            "epub" => Icons.Material.Filled.Book,
            _ => Icons.Material.Filled.InsertDriveFile
        };
    }

    private void RemoveFile(IBrowserFile file) {
        if (!_isProcessing) {
            _uploadedFiles.Remove(file);
            Snackbar.Add($"已移除 {file.Name}", Severity.Info);
        }
    }

    private void ClearAll() {
        if (!_isProcessing) {
            _uploadedFiles.Clear();
            _downloadUrl = string.Empty;
            StateHasChanged();
        }
    }

    private async Task MergeFiles() {
        if (!_uploadedFiles.Any()) {
            Snackbar.Add("请先上传文件", Severity.Warning);
            return;
        }

        _isProcessing = true;
        _processedFiles = 0;
        _totalFiles = _uploadedFiles.Count;
        _currentStatus = "准备开始合并...";
        _downloadUrl = string.Empty; // 重置下载URL
        StateHasChanged();

        var tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
        var outputDir = Path.Combine(Env.WebRootPath, "tmp");
        Directory.CreateDirectory(tempDir);
        Directory.CreateDirectory(outputDir);

        List<string> pdfFiles = [];

        try {
            // 1. 保存上传的文件到临时目录
            for (var i = 0; i < _uploadedFiles.Count; i++) {
                var file = _uploadedFiles[i];
                _currentProcessingFile = file.Name;
                _currentStatus = $"正在处理文件 ({i + 1}/{_uploadedFiles.Count})...";
                StateHasChanged();

                var filePath = await SaveUploadedFile(file, tempDir) ?? string.Empty;
                await using var stream = file.OpenReadStream(50 * 1024 * 1024);
                await using var fileStream = new FileStream(filePath, FileMode.Create);
                await stream.CopyToAsync(fileStream);

                var ext = Path.GetExtension(filePath).ToLowerInvariant();
                var pdfPath = filePath;

                // 2. 转换非PDF文件为PDF
                if (ext != ".pdf") {
                    _currentStatus = $"正在转换 {file.Name} 为PDF...";
                    StateHasChanged();

                    await ConvertToPdfAsync(filePath);
                    pdfPath = Path.ChangeExtension(filePath, ".pdf");
                    if (!File.Exists(pdfPath)) {
                        Snackbar.Add($"转换失败: {file.Name}", Severity.Error);
                        continue;
                    }
                }

                pdfFiles.Add(pdfPath);
                _processedFiles++;
                StateHasChanged();
            }

            if (!pdfFiles.Any()) {
                Snackbar.Add("没有有效的文件可合并", Severity.Error);
                return;
            }

            // 3. 合并PDF
            _currentStatus = "正在合并PDF文件...";
            StateHasChanged();

            var outputFileName = $"merged_{DateTime.Now:yyyyMMddHHmmss}.pdf";
            var outputPath = Path.Combine(outputDir, outputFileName);

            await Task.Run(() => MergePdfFiles(pdfFiles, outputPath));

            // 4. 设置下载链接
            _currentStatus = "合并完成！";
            _downloadUrl = $"/tmp/{outputFileName}";
            Snackbar.Add("PDF合并完成，请点击下载按钮保存文件", Severity.Success);
        }
        catch (Exception ex) {
            Snackbar.Add($"合并失败: {ex.Message}", Severity.Error);
        }
        finally {
            _isProcessing = false;
            _currentProcessingFile = null;
            _currentStatus = "";
            _processedFiles = 0;
            StateHasChanged();

            // 清理临时文件
            try {
                if (Directory.Exists(tempDir)) {
                    Directory.Delete(tempDir, true);
                }
            }
            catch (Exception ex) {
                Console.WriteLine($"清理临时文件时出错: {ex.Message}");
            }
        }
    }

    private async Task ConvertToPdfAsync(string inputFile) {
        var ext = Path.GetExtension(inputFile).ToLowerInvariant();

        //EPUB files
        if (ext == ".epub") {
            await ConvertEpubToPdfAsync(inputFile);
            return;
        }

        var libreOfficePath = IntranetResourceSite.Value.LibreOfficePath;

        if (libreOfficePath is null || libreOfficePath.isEmpty()) {
            Snackbar.Add("LibreOffice path is not configured.", Severity.Error);
        }

        var outputDir = Path.GetDirectoryName(inputFile);
        if (string.IsNullOrEmpty(outputDir) || !Directory.Exists(outputDir)) {
            Snackbar.Add("Output directory not found.", Severity.Error);
        }

        //压缩转换质量
        var arguments = $"--headless --convert-to pdf:writer_pdf_Export:Quality=85,ReduceImageResolution=true,MaxImageResolution=150 --outdir {outputDir} {inputFile}";
        //不压缩转换质量
        // string arguments = $"--headless --convert-to pdf --outdir {outputDir} {inputFile}";

        var process = new Process {
            StartInfo = new ProcessStartInfo {
                FileName = libreOfficePath,
                Arguments = arguments,
                UseShellExecute = false,
                CreateNoWindow = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                WorkingDirectory = Path.GetDirectoryName(libreOfficePath)
            }
        };

        process.Start();

        // 等待进程完成，设置超时时间（例如2分钟）
        if (!process.WaitForExit(120000)) {
            process.Kill();
            Snackbar.Add("文件转换超时", Severity.Error);
        }

        if (process.ExitCode != 0) {
            var error = await process.StandardError.ReadToEndAsync();

            Snackbar.Add($"转换失败: {error}", Severity.Error);
        }
    }

    private async Task ConvertEpubToPdfAsync(string inputFile) {
        try {
            _currentStatus = "正在转换EPUB文件...";
            await InvokeAsync(StateHasChanged);

            string calibrePath = IntranetResourceSite.Value.CalibrePath;
            if (calibrePath is null || calibrePath.isEmpty()) {
                Snackbar.Add("CalibrePath path is not configured.", Severity.Error);
            }

            string outputFile = Path.ChangeExtension(inputFile, ".pdf");

            var process = new Process {
                StartInfo = new ProcessStartInfo {
                    FileName = calibrePath,
                    Arguments = $"\"{inputFile}\" \"{outputFile}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                }
            };

            process.Start();

            if (!process.WaitForExit(120000)) {
                process.Kill();
                Snackbar.Add("文件转换超时", Severity.Error);
            }

            if (process.ExitCode != 0) {
                var error = await process.StandardError.ReadToEndAsync();
                Snackbar.Add("文件转换失败", Severity.Error);
            }

            if (!File.Exists(outputFile)) {
                Snackbar.Add("文件转换失败", Severity.Error);
            }
        }
        catch (Exception ex) {
            Snackbar.Add($"EPUB转换失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task MergePdfFiles(List<string> pdfFiles, string outputFile) {
        using var outputDocument = new PdfDocument();
        foreach (var file in pdfFiles) {
            if (!File.Exists(file)) {
                Snackbar.Add($"文件不存在: {file}", Severity.Error);
                return;
            }
        }

        //需要在这里解密
        try {
            _currentStatus = $"正在解密文件中，请稍后...";
            await InvokeAsync(StateHasChanged);
            // 批量解密文件
            await EncryptFilesService.EncryptFilesFromFolder();
            await DecryptFilesService.DecryptFilesFromPath(pdfFiles);
        }
        catch (Exception ex) {
            Snackbar.Add($"解密文件时出错: {ex.Message}", Severity.Error);
        }

        try {
            foreach (var file in pdfFiles) {
                using var inputDocument = PdfReader.Open(file, PdfDocumentOpenMode.Import);
                for (var i = 0; i < inputDocument.PageCount; i++) {
                    outputDocument.AddPage(inputDocument.Pages[i]);
                }
            }
        }
        catch (Exception ex) {
            Snackbar.Add($"处理文件时出错: {ex.Message}", Severity.Error);
        }


        if (outputDocument.PageCount == 0) {
            Snackbar.Add("没有有效的页面可合并", Severity.Error);
        }

        outputDocument.Save(outputFile);
    }

    private string SanitizeFileName(string fileName) {
        if (string.IsNullOrWhiteSpace(fileName))
            return string.Empty;

        // 替换空格为下划线
        var sanitized = fileName.Replace(" ", "_");

        // 移除其他可能引起问题的字符
        var invalidChars = Path.GetInvalidFileNameChars();
        foreach (var c in invalidChars) {
            sanitized = sanitized.Replace(c.ToString(), "");
        }

        return sanitized.Trim();
    }

    private async Task<string?> SaveUploadedFile(IBrowserFile file, string targetDirectory) {
        try {
            // 确保目录存在
            if (!Directory.Exists(targetDirectory)) {
                Directory.CreateDirectory(targetDirectory);
            }

            // 清理文件名
            var safeFileName = SanitizeFileName(file.Name);
            var filePath = Path.Combine(targetDirectory, safeFileName);

            // 保存文件
            await using var stream = file.OpenReadStream(50 * 1024 * 1024);
            await using var fileStream = new FileStream(filePath, FileMode.Create);
            await stream.CopyToAsync(fileStream);

            return filePath;
        }
        catch (Exception ex) {
            Snackbar.Add($"保存上传文件时出错: {ex.Message}", Severity.Error);
            return null;
        }
    }

    private async Task DownloadFile() {
        if (string.IsNullOrEmpty(_downloadUrl)) {
            Snackbar.Add("下载链接为空！: ", Severity.Error);
            return;
        }

        try {
            // 使用 JS 在新标签页打开下载链接
            await JSRuntime.InvokeVoidAsync("open", _downloadUrl, "_blank");
            Snackbar.Add("下载已开始，请查看浏览器下载内容", Severity.Success);
        }
        catch (Exception ex) {
            Snackbar.Add("下载文件时出错: " + ex.Message, Severity.Error);
        }
    }

}