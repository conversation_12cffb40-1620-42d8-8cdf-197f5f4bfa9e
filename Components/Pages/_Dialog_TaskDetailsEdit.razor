@using SuntechApp.Data

<MudDialog>
    <DialogContent>
        <MudCard Outlined="true">
            <MudCardContent>
                <MudForm>
                    <MudNumericField Label="行号" @bind-Value="_taskDetails.seq" Required></MudNumericField>
                    <MudTextField Label="报表Url" @bind-Value="_taskDetails.url" Required AutoGrow></MudTextField>
                    <MudTextField Label="标题" @bind-Value="_taskDetails.subject" Required></MudTextField>
                    <MudTextField Label="内容" @bind-Value="_taskDetails.body" Required AutoGrow></MudTextField>
                    <MudTextField Label="收件人" @bind-Value="_taskDetails.toaddress" Required></MudTextField>
                    <MudTextField Label="抄送人" @bind-Value="_taskDetails.ccaddress"></MudTextField>
                    <MudTextField Label="报表发送名称" @bind-Value="_taskDetails.filename"></MudTextField>
                    <MudSelect Label="扩展后缀" @bind-Value="_taskDetails.ext" Required>
                        @foreach (var ext in _ext)
                        {
                            <MudSelectItem  Value="ext">@ext</MudSelectItem>
                        }
                    </MudSelect>
                </MudForm>
            </MudCardContent>
        </MudCard>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color=Color.Primary Variant="Variant.Filled" OnClick="Submit">确定</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter]
    private IMudDialogInstance MudDialog { get; set; }
    private readonly string[] _ext =
    {
        "xlsx", "xls","pdf"
    };

    [Parameter]
    public TaskDetails _taskDetails { get; set; }

    private void Submit() => MudDialog.Close(DialogResult.Ok(_taskDetails));

    private void Cancel() => MudDialog.Cancel();
}


