@page "/ExamStatistics"
@attribute [Authorize]
@inject ISqlSugarClient db
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Authorization
@using SqlSugar
@using SuntechApp.Data

<PageTitle>考试统计</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <div class="d-flex align-center mb-6">
        <MudIcon Icon="@Icons.Material.Filled.Analytics" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
        <MudText Typo="Typo.h5" Color="Color.Primary">考试统计分析</MudText>
    </div>

    @if (loading)
    {
        <div class="d-flex justify-center align-center" style="height: 200px;">
            <MudProgressCircular Indeterminate="true" Size="Size.Large" />
        </div>
    }
    else
    {
        <!-- 总体统计卡片 -->
        <MudGrid Class="mb-6">
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="4" Class="pa-4">
                    <MudCardContent Class="text-center">
                        <MudIcon Icon="@Icons.Material.Filled.Quiz" Color="Color.Primary" Size="Size.Large" Class="mb-2" />
                        <MudText Typo="Typo.h4" Color="Color.Primary">@totalExams</MudText>
                        <MudText Typo="Typo.body1">总考试数</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="4" Class="pa-4">
                    <MudCardContent Class="text-center">
                        <MudIcon Icon="@Icons.Material.Filled.People" Color="Color.Info" Size="Size.Large" Class="mb-2" />
                        <MudText Typo="Typo.h4" Color="Color.Info">@totalSubmissions</MudText>
                        <MudText Typo="Typo.body1">总提交数</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="4" Class="pa-4">
                    <MudCardContent Class="text-center">
                        <MudIcon Icon="@Icons.Material.Filled.Grading" Color="Color.Success" Size="Size.Large" Class="mb-2" />
                        <MudText Typo="Typo.h4" Color="Color.Success">@gradedSubmissions</MudText>
                        <MudText Typo="Typo.body1">已评分数</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="4" Class="pa-4">
                    <MudCardContent Class="text-center">
                        <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Color="Color.Warning" Size="Size.Large" Class="mb-2" />
                        <MudText Typo="Typo.h4" Color="Color.Warning">@averageScore.ToString("F1")</MudText>
                        <MudText Typo="Typo.body1">总平均分</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- 各考试详细统计 -->
        <MudPaper Class="pa-4" Elevation="3">
            <div class="d-flex align-center justify-space-between mb-4">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.TableChart" Class="mr-2" Color="Color.Primary" />
                    <MudText Typo="Typo.h5">各考试详细统计</MudText>
                    @if (selectedExamId.HasValue && availableExams.Any())
                    {
                        var selectedExam = availableExams.FirstOrDefault(e => e.Id == selectedExamId.Value);
                        <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-3">
                            当前考试：@(selectedExam?.Title ?? "未知考试")
                        </MudChip>
                    }
                </div>
                <div class="d-flex align-center gap-3">
                    <MudSelect T="int?"
                             Value="selectedExamId"
                             Label="选择考试"
                             Variant="Variant.Outlined"
                             Margin="Margin.Dense"
                             Style="min-width: 200px;"
                             ValueChanged="OnExamSelectionChanged">
                        <MudSelectItem T="int?" Value="null">全部考试</MudSelectItem>
                        @foreach (var exam in availableExams)
                        {
                            <MudSelectItem T="int?" Value="exam.Id">
                                @exam.Title (@exam.SubmissionCount 人提交)
                            </MudSelectItem>
                        }
                    </MudSelect>
                    <MudButton Color="Color.Success"
                             StartIcon="@Icons.Material.Filled.Download"
                             OnClick="ExportStatistics"
                             Variant="Variant.Outlined"
                             Size="Size.Small">
                        导出统计报告
                    </MudButton>
                    <MudButton Color="Color.Primary"
                             StartIcon="@Icons.Material.Filled.FileDownload"
                             OnClick="@(() => ExportResults())"
                             Variant="Variant.Outlined"
                             Size="Size.Small">
                        @(selectedExamId.HasValue ? "导出选中考试结果" : "导出所有结果")
                    </MudButton>
                </div>
            </div>

            <MudDataGrid T="ExamStatisticItem" Items="@examStatistics" Groupable="false"
                         SortMode="SortMode.Multiple" Filterable="true"
                         Hover="true" Bordered="true" Dense="true">
                <ToolBarContent>
                    <MudText Typo="Typo.h6">考试统计表</MudText>
                    <MudSpacer />
                    @if (selectedExamId.HasValue)
                    {
                        <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                            显示单个考试统计
                        </MudChip>
                    }
                    else
                    {
                        <MudChip T="string" Color="Color.Info" Size="Size.Small">
                            显示全部考试统计
                        </MudChip>
                    }
                </ToolBarContent>
                <Columns>
                    <PropertyColumn Property="x => x.ExamTitle" Title="考试名称" />
                    <PropertyColumn Property="x => x.SubmissionCount" Title="提交人数" />
                    <PropertyColumn Property="x => x.GradedCount" Title="已评分" />
                    <PropertyColumn Property="x => x.CompletionRate" Title="完成率" Format="P1" />
                    <PropertyColumn Property="x => x.AverageScore" Title="平均分" Format="F1">
                        <CellTemplate>
                            @if (context.Item.AverageScore.HasValue)
                            {
                                <MudChip T="string" Color="@GetScoreColor(context.Item.AverageScore.Value)" Size="Size.Small">
                                    @context.Item.AverageScore.Value.ToString("F1")
                                </MudChip>
                            }
                            else
                            {
                                <MudChip T="string" Color="Color.Default" Size="Size.Small">未评分</MudChip>
                            }
                        </CellTemplate>
                    </PropertyColumn>
                    <PropertyColumn Property="x => x.HighestScore" Title="最高分" Format="F1" />
                    <PropertyColumn Property="x => x.LowestScore" Title="最低分" Format="F1" />
                    <PropertyColumn Property="x => x.PassRate" Title="通过率" Format="P1">
                        <CellTemplate>
                            @if (context.Item.PassRate.HasValue)
                            {
                                <MudProgressLinear Color="@GetPassRateColor(context.Item.PassRate.Value)" 
                                                 Value="@((double)(context.Item.PassRate.Value * 100))" 
                                                 Class="my-2" />
                                <MudText Typo="Typo.caption">@context.Item.PassRate.Value.ToString("P1")</MudText>
                            }
                            else
                            {
                                <MudText Typo="Typo.caption">-</MudText>
                            }
                        </CellTemplate>
                    </PropertyColumn>
                    <TemplateColumn Title="操作" Sortable="false">
                        <CellTemplate>
                            <MudTooltip Text="导出此考试结果">
                                <MudIconButton Icon="@Icons.Material.Filled.FileDownload"
                                             Color="Color.Primary"
                                             Size="Size.Small"
                                             OnClick="() => ExportExamResults(context.Item.ExamId)" />
                            </MudTooltip>
                        </CellTemplate>
                    </TemplateColumn>
                </Columns>
                <PagerContent>
                    <MudDataGridPager PageSizeOptions="new int[]{10, 25, 50}" />
                </PagerContent>
            </MudDataGrid>
        </MudPaper>
    }
</MudContainer>

@code {
    private SqlSugarProvider DefaultDb => db.AsTenant().GetConnection("Default");
    private bool loading = true;

    // 考试选择相关
    private List<ExamOption> availableExams = new();
    private int? selectedExamId = null;

    // 总体统计数据
    private int totalExams = 0;
    private int totalSubmissions = 0;
    private int gradedSubmissions = 0;
    private decimal averageScore = 0;

    // 各考试统计数据
    private List<ExamStatisticItem> examStatistics = new();
    private List<ExamStatisticItem> allExamStatistics = new(); // 存储所有统计数据

    protected override async Task OnInitializedAsync()
    {
        await LoadStatistics();
        loading = false;
    }

    // 移除搜索过滤器，改用考试选择

    private async Task LoadStatistics()
    {
        try
        {
            // 加载总体统计
            totalExams = await DefaultDb.Queryable<ExamPaper>().CountAsync();
            totalSubmissions = await DefaultDb.Queryable<ExamSubmission>().CountAsync();
            gradedSubmissions = await DefaultDb.Queryable<ExamSubmission>()
                .Where(x => x.Score != null)
                .CountAsync();
            
            var avgScore = await DefaultDb.Queryable<ExamSubmission>()
                .Where(x => x.Score != null)
                .AvgAsync(x => x.Score);
            averageScore = avgScore ?? 0;

            // 加载各考试统计
            var examStats = await DefaultDb.Queryable<ExamPaper>()
                .LeftJoin<ExamSubmission>((p, s) => p.Id == s.ExamPaperId)
                .GroupBy((p, s) => new { p.Id, p.Title })
                .Select((p, s) => new
                {
                    ExamId = p.Id,
                    ExamTitle = p.Title,
                    SubmissionCount = SqlFunc.AggregateCount(s.Id),
                    GradedCount = SqlFunc.AggregateSum(SqlFunc.IIF(s.Score != null, 1, 0)),
                    AverageScore = SqlFunc.AggregateAvg(s.Score),
                    HighestScore = SqlFunc.AggregateMax(s.Score),
                    LowestScore = SqlFunc.AggregateMin(s.Score)
                })
                .ToListAsync();

            // 生成所有考试统计数据
            allExamStatistics = new List<ExamStatisticItem>();
            foreach (var x in examStats)
            {
                var passRate = x.GradedCount > 0 && x.AverageScore.HasValue ?
                    await GetPassRate(x.ExamId) : null;

                allExamStatistics.Add(new ExamStatisticItem
                {
                    ExamId = x.ExamId,
                    ExamTitle = x.ExamTitle,
                    SubmissionCount = x.SubmissionCount,
                    GradedCount = x.GradedCount,
                    CompletionRate = x.SubmissionCount > 0 ? (decimal)x.GradedCount / x.SubmissionCount : 0,
                    AverageScore = x.AverageScore,
                    HighestScore = x.HighestScore,
                    LowestScore = x.LowestScore,
                    PassRate = passRate
                });
            }

            // 生成可选择的考试列表
            availableExams = allExamStatistics
                .Select(s => new ExamOption
                {
                    Id = s.ExamId,
                    Title = s.ExamTitle,
                    SubmissionCount = s.SubmissionCount
                })
                .OrderBy(e => e.Title)
                .ToList();

            // 根据选择的考试筛选统计数据
            FilterStatisticsByExam();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载统计数据失败: {ex.Message}", Severity.Error);
        }
    }

    private void FilterStatisticsByExam()
    {
        if (selectedExamId.HasValue)
        {
            examStatistics = allExamStatistics.Where(s => s.ExamId == selectedExamId.Value).ToList();
        }
        else
        {
            examStatistics = allExamStatistics.ToList();
        }
        StateHasChanged();
    }

    private async Task OnExamSelectionChanged(int? examId)
    {
        selectedExamId = examId;
        FilterStatisticsByExam();
    }

    private async Task<decimal?> GetPassRate(int examId)
    {
        try
        {
            var totalGraded = await DefaultDb.Queryable<ExamSubmission>()
                .Where(x => x.ExamPaperId == examId && x.Score != null)
                .CountAsync();
            
            if (totalGraded == 0) return null;
            
            var passedCount = await DefaultDb.Queryable<ExamSubmission>()
                .Where(x => x.ExamPaperId == examId && x.Score >= 60)
                .CountAsync();
            
            return (decimal)passedCount / totalGraded;
        }
        catch
        {
            return null;
        }
    }

    private Color GetScoreColor(decimal score)
    {
        if (score >= 90) return Color.Success;
        if (score >= 80) return Color.Info;
        if (score >= 60) return Color.Warning;
        return Color.Error;
    }

    private Color GetPassRateColor(decimal passRate)
    {
        if (passRate >= 0.8m) return Color.Success;
        if (passRate >= 0.6m) return Color.Info;
        if (passRate >= 0.4m) return Color.Warning;
        return Color.Error;
    }

    // 导出方法
    private async Task ExportStatistics()
    {
        try
        {
            var url = "/api/ExamFile/export-exam-statistics";
            await JSRuntime.InvokeVoidAsync("open", url, "_blank");
            Snackbar.Add("统计报告导出已开始，请查看浏览器下载内容", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"导出失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task ExportResults()
    {
        try
        {
            string url;
            string message;

            if (selectedExamId.HasValue)
            {
                var selectedExam = availableExams.FirstOrDefault(e => e.Id == selectedExamId.Value);
                var examTitle = selectedExam?.Title ?? "未知考试";
                url = $"/api/ExamFile/export-exam-results?examId={selectedExamId}";
                message = $"正在导出《{examTitle}》的考试结果，请查看浏览器下载内容";
            }
            else
            {
                url = "/api/ExamFile/export-exam-results";
                message = "正在导出所有考试结果，请查看浏览器下载内容";
            }

            await JSRuntime.InvokeVoidAsync("open", url, "_blank");
            Snackbar.Add(message, Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"导出失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task ExportExamResults(int examId)
    {
        try
        {
            var url = $"/api/ExamFile/export-exam-results?examId={examId}";
            await JSRuntime.InvokeVoidAsync("open", url, "_blank");
            Snackbar.Add("考试结果导出已开始，请查看浏览器下载内容", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"导出失败: {ex.Message}", Severity.Error);
        }
    }

    // 考试统计项模型
    public class ExamStatisticItem
    {
        public int ExamId { get; set; }
        public string ExamTitle { get; set; } = string.Empty;
        public int SubmissionCount { get; set; }
        public int GradedCount { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal? AverageScore { get; set; }
        public decimal? HighestScore { get; set; }
        public decimal? LowestScore { get; set; }
        public decimal? PassRate { get; set; }
    }

    // 考试选项数据结构
    public class ExamOption
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public int SubmissionCount { get; set; }
    }
}
