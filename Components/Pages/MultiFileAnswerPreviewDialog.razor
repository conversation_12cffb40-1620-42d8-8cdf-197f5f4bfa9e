@inject IJSRuntime JSRuntime
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <div class="d-flex align-center mb-4">
            <MudIcon Icon="@Icons.Material.Filled.Assignment" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
            <div>
                <MudText Typo="Typo.h5" Color="Color.Primary">学生答案预览</MudText>
                <MudText Typo="Typo.body2" Color="Color.Info">@Submission?.StudentName (@Submission?.StudentNumber)</MudText>
            </div>
        </div>

        @if (loading)
        {
            <div class="d-flex justify-center align-center" style="height: 400px;">
                <MudProgressCircular Indeterminate="true" Size="Size.Large" />
                <MudText Class="ml-3">正在加载预览...</MudText>
            </div>
        }
        else if (Submission?.Files?.Any() != true)
        {
            <MudAlert Severity="Severity.Warning" Class="mb-4">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Warning" Class="mr-2" />
                    <div>
                        <MudText Typo="Typo.body1" Class="font-weight-medium">没有找到答案文件</MudText>
                        <MudText Typo="Typo.body2">该提交没有关联的文件</MudText>
                    </div>
                </div>
            </MudAlert>
        }
        else
        {
            <!-- 提交信息卡片 -->
            <MudCard Class="mb-4" Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.Info" Class="mr-2" />
                            提交信息
                        </MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        @if (Submission?.Score.HasValue == true)
                        {
                            <MudChip T="string" Color="Color.Success" Icon="@Icons.Material.Filled.Star">
                                已评分：@Submission.Score.Value.ToString("F1")分
                            </MudChip>
                        }
                        else
                        {
                            <MudChip T="string" Color="Color.Warning" Icon="@Icons.Material.Filled.Schedule">
                                待评分
                            </MudChip>
                        }
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">学生信息：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@Submission?.StudentName (@Submission?.StudentNumber)</MudText>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">提交时间：</MudText>
                            </div>
                            <MudText Typo="Typo.body1" Class="ml-6">@Submission?.SubmittedDate.ToString("yyyy年MM月dd日 HH:mm")</MudText>
                        </MudItem>
                        <MudItem xs="12">
                            <div class="d-flex align-center mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.AttachFile" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                <MudText Typo="Typo.body2" Class="font-weight-medium">答案文件（@Submission.Files.Count 个）：</MudText>
                            </div>
                            <div class="ml-6">
                                @foreach (var file in Submission.Files.OrderBy(f => f.DisplayOrder))
                                {
                                    <div class="d-flex align-center mb-1">
                                        <MudIcon Icon="@GetFileIcon(file.FileType)" Size="Size.Small" Class="mr-2" />
                                        <MudText Typo="Typo.body2" Class="mr-2">@file.FileName</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">(@FormatFileSize(file.FileSize))</MudText>
                                    </div>
                                }
                            </div>
                        </MudItem>
                        @if (!string.IsNullOrEmpty(Submission?.Comments))
                        {
                            <MudItem xs="12">
                                <div class="d-flex align-center mb-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Comment" Size="Size.Small" Class="mr-2" Color="Color.Info" />
                                    <MudText Typo="Typo.body2" Class="font-weight-medium">评语：</MudText>
                                </div>
                                <MudPaper Class="pa-3 ml-6" Elevation="1">
                                    <MudText Typo="Typo.body2">@Submission.Comments</MudText>
                                </MudPaper>
                            </MudItem>
                        }
                    </MudGrid>
                </MudCardContent>
            </MudCard>

            <!-- 文件预览区域 -->
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.Preview" Class="mr-2" />
                            文件预览
                        </MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (Submission.Files.Count == 1)
                    {
                        <!-- 单文件直接预览 -->
                      
                    }
                    else
                    {
                        <!-- 多文件标签页预览 -->
                        <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
                            @foreach (var file in Submission.Files.OrderBy(f => f.DisplayOrder))
                            {
                                <MudTabPanel Text="@file.FileName" Icon="@GetFileIcon(file.FileType)">
                                    
                                </MudTabPanel>
                            }
                        </MudTabs>
                    }
                </MudCardContent>
            </MudCard>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel" 
                 StartIcon="@Icons.Material.Filled.Close"
                 Color="Color.Default">
            关闭
        </MudButton>
        @if (Submission?.Files?.Any() == true)
        {
            <MudButton Color="Color.Primary"
                     OnClick="DownloadAllFiles"
                     StartIcon="@Icons.Material.Filled.Download"
                     Variant="Variant.Outlined"
                     Class="mr-2">
                逐个下载
            </MudButton>
            <MudButton Color="Color.Secondary"
                     OnClick="DownloadAsZip"
                     StartIcon="@Icons.Material.Filled.Archive"
                     Variant="Variant.Outlined">
                打包下载
            </MudButton>
        }
        @if (Submission?.Score == null)
        {
            <MudButton Color="Color.Success" 
                     OnClick="StartGrading" 
                     StartIcon="@Icons.Material.Filled.Grading"
                     Variant="Variant.Filled">
                开始评分
            </MudButton>
        }
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] IMudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public SuntechApp.Components.Pages.ExamGrading.ExamSubmissionView Submission { get; set; } = null!;
    
    private bool loading = true;

    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(500); // 模拟加载
        loading = false;
        StateHasChanged();
    }
    

    private bool IsImageFile(string extension)
    {
        return new[] { ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp" }.Contains(extension);
    }

    private bool IsTextFile(string extension)
    {
        return new[] { ".txt", ".csv", ".cs", ".js", ".html", ".css", ".sql", ".json", ".xml" }.Contains(extension);
    }

    private bool IsOfficeFile(string extension)
    {
        return new[] { ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx" }.Contains(extension);
    }
    

    private async Task OpenOfficePreview(string fileUrl)
    {
        try
        {
            var previewUrl = $"https://view.officeapps.live.com/op/embed.aspx?src={Uri.EscapeDataString(fileUrl)}";
            


            await JSRuntime.InvokeVoidAsync("open", previewUrl, "_blank");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"打开预览失败: {ex.Message}", Severity.Error);
        }
    }

    private string GetFileIcon(string? fileType)
    {
        return fileType?.ToLower() switch
        {
            "pdf" => Icons.Material.Filled.PictureAsPdf,
            "docx" or "doc" => Icons.Material.Filled.Description,
            "xlsx" or "xls" => Icons.Material.Filled.TableChart,
            "pptx" or "ppt" => Icons.Material.Filled.Slideshow,
            "png" or "jpg" or "jpeg" or "gif" or "bmp" => Icons.Material.Filled.Image,
            "zip" or "rar" or "7z" => Icons.Material.Filled.Archive,
            "txt" => Icons.Material.Filled.TextSnippet,
            "cs" or "js" or "html" or "css" or "sql" => Icons.Material.Filled.Code,
            "mp4" or "avi" or "mov" => Icons.Material.Filled.VideoFile,
            _ => Icons.Material.Filled.AttachFile
        };
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
        return $"{bytes / (1024 * 1024):F1} MB";
    }

    private async Task DownloadAllFiles()
    {
        try
        {
            if (Submission?.Files?.Any() == true)
            {
                foreach (var file in Submission.Files)
                {
                    var url = $"/api/ExamFile/download-answer/{Path.GetFileName(file.FilePath)}";
                    await JSRuntime.InvokeVoidAsync("open", url, "_blank");
                    await Task.Delay(500); // 避免同时下载太多文件
                }
                Snackbar.Add($"开始逐个下载 {Submission.Files.Count} 个文件", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"下载失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DownloadAsZip()
    {
        try
        {
            if (Submission?.Id > 0)
            {
                var url = $"/api/ExamFile/download-submission-zip/{Submission.Id}";
                await JSRuntime.InvokeVoidAsync("open", url, "_blank");
                Snackbar.Add("开始下载打包文件", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"打包下载失败: {ex.Message}", Severity.Error);
        }
    }

    private void StartGrading()
    {
        MudDialog.Close(DialogResult.Ok("start_grading"));
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
