@using MudBlazor
@using SuntechApp.Data
@inject ISnackbar Snackbar
<MudDialog Style="width: 61.8%;">
    <TitleContent>@(IsEditMode ? "编辑分类" : "添加分类")</TitleContent>
    <DialogContent>
        <MudForm @ref="_form" @bind-IsValid="_isValid" @bind-Errors="_errors">
            <MudTextField @bind-Value="_item.Class"
                          Label="分类名称"
                          Required="true"
                          RequiredError="分类名称不能为空"
                          Class="mb-4"/>

            <MudSelect T="int" @bind-Value="_item.ParentId"
                       Label="父级分类"
                       Class="mb-4"
                       Required="false">
                <MudSelectItem Value="0">无 (顶级分类)</MudSelectItem>
                @foreach (var parent in _availableParents) {
                    <MudSelectItem Value="@parent.Id">@parent.Class</MudSelectItem>
                }
            </MudSelect>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@Cancel" Variant="Variant.Text">取消</MudButton>
        <MudButton OnClick="@Save"
                   Color="Color.Primary"
                   Variant="Variant.Filled"
                   Disabled="!_isValid">保存
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] public IMudDialogInstance MudDialog { get; set; }

    [Parameter] public TrainingMaterialClass Item { get; set; }

    [Parameter] public List<TrainingMaterialClass> AllItems { get; set; } = new();

    private bool IsEditMode => Item?.Id > 0;
    private TrainingMaterialClass _item = new();
    private List<TrainingMaterialClass> _availableParents = new();
    private MudForm _form;
    private bool _isValid;
    private string[] _errors = [];

    protected override void OnInitialized() {
        // 初始化表单数据
        if (IsEditMode) {
            _item = new TrainingMaterialClass {
                Id = Item.Id,
                Class = Item.Class,
                ParentId = Item.ParentId,
            };
        }
        else {
            _item = new TrainingMaterialClass {
                ParentId = 0,
            };
        }

        // 准备可选的父级分类列表
        _availableParents = AllItems
            .Where(x => !IsEditMode || x.Id != Item.Id) // 编辑时排除自身
            .OrderBy(x => x.Class)
            .ToList();
    }

    private async Task Save() {
        await _form.Validate();

        if (!_form.IsValid)
            return;

        try {
            // 确保 ParentId 是有效的
            if (_item.ParentId <= 0) {
                _item.ParentId = 0; // 设置为0表示没有父级
            }

            MudDialog.Close(DialogResult.Ok(_item));
        }
        catch (Exception ex) {
            Snackbar.Add($"保存失败: {ex.Message}", Severity.Error);
        }
    }

    private void Cancel() {
        MudDialog.Cancel();
    }

}