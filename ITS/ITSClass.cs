namespace SuntechApp.ITS
{
    public class ITSEmailSettings
    {
        public string? EmailAccount { get; set; }
        public string? EmailAccountName { get; set; }
        public string? EmailPassword { get; set; }
        public string? SmtpServer { get; set; }
        public string? SmtpServerPort { get; set; }
        public string? MaxRetries { get; set; }
        public string? DelayMinute { get; set; }
        public string? FirstDelaySecond { get; set; }
        public string? DefaultSuffix { get; set; }
    }
    public class ITSEmailAttachment
    {
        public string? AttachmentPath { get; set; }
        public string? FileName { get; set; }
    }
}
