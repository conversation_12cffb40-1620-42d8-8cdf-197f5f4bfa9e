using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Options;

namespace SuntechApp.IpgPlugin;

public class DecryptFilesService(
    ILogger<DecryptFilesService> logger,
    IOptions<IpgJsonSettings> ipgJsonSettings,
    IHttpClientFactory httpClientFactory) {
    private string? _loginId;
    private DateTime _loginTime;
    private readonly object _loginLock = new();
    private bool _isLoggingIn;
    private readonly TimeSpan _loginExpire = TimeSpan.FromMinutes(25);
    private readonly HttpClient _httpClient = httpClientFactory.CreateClient("Client");
    private readonly IpgJsonSettings _ipgJsonSettings = ipgJsonSettings.Value;
    private readonly ILogger<DecryptFilesService> _logger = logger;

    private async Task<bool> LoginAsync() {
        // 使用双重检查锁定模式，避免不必要的锁竞争
        if (IsLoginValid()) return true;

        // 防止并发登录
        lock (_loginLock) {
            if (IsLoginValid()) return true;
            if (_isLoggingIn) {
                // 如果已经在登录中，等待登录完成
                Monitor.Wait(_loginLock);
                return IsLoginValid();
            }

            _isLoggingIn = true;
        }

        try {
            var loginRequest = new IpgJsonLoginRequest {
                Name = _ipgJsonSettings.LoginName,
                Password = _ipgJsonSettings.Password
            };

            string jsonRequest = JsonSerializer.Serialize(loginRequest);
            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                _ipgJsonSettings.IpgJsonUrl + "login",
                content
            );

            if (response.IsSuccessStatusCode) {
                string jsonResponse = await response.Content.ReadAsStringAsync();
                var loginResponse = JsonSerializer.Deserialize<IpgJsonResponse>(jsonResponse);

                if (loginResponse is not null && loginResponse.error == "0") {
                    lock (_loginLock) {
                        _loginId = loginResponse.loginid;
                        _loginTime = DateTime.Now;
                        _logger.LogInformation($"登录成功! LoginID:{_loginId}---LoginTime:{_loginTime}");
                        return true;
                    }
                }
            }

            _logger.LogWarning("登录失败");
            return false;
        }
        catch (Exception ex) {
            _logger.LogError($"登录过程中发生异常：{ex.Message}");
            return false;
        }
        finally {
            lock (_loginLock) {
                _isLoggingIn = false;
                Monitor.PulseAll(_loginLock); // 通知等待的线程
            }
        }
    }

    private async Task LogoutAsync() {
        string? loginIdToLogout;

        // 原子地获取并清除登录状态
        lock (_loginLock) {
            if (string.IsNullOrEmpty(_loginId)) return;
            loginIdToLogout = _loginId;
            _loginId = null;
        }

        try {
            var logoutRequest = new IpgJsonLogoutRequest { LoginID = loginIdToLogout };
            string jsonRequest = JsonSerializer.Serialize(logoutRequest);
            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                _ipgJsonSettings.IpgJsonUrl + "logout",
                content
            );

            string jsonResponse = await response.Content.ReadAsStringAsync();
            var logoutResponse = JsonSerializer.Deserialize<IpgJsonResponse>(jsonResponse);

            if (logoutResponse != null && logoutResponse.error == "0") {
                _logger.LogInformation("注销成功");
            }
        }
        catch (Exception ex) {
            _logger.LogError($"注销过程中发生异常：{ex.Message}");
        }
    }

    private bool IsLoginValid() {
        lock (_loginLock) {
            return !string.IsNullOrEmpty(_loginId) && (DateTime.Now - _loginTime) < _loginExpire;
        }
    }

    public async Task DecryptFilesFromPath(List<string>? filesToDecrypt, int maxRetryCount = 3) {
        if (filesToDecrypt is null || filesToDecrypt.Count == 0) {
            _logger.LogWarning("没有需要解密的文件");
            return;
        }

        int retryCount = 0;
        bool shouldRetry;

        do {
            shouldRetry = false;

            // 检查登录状态，如果未登录或已过期则重新登录
            if (!IsLoginValid()) {
                if (!await LoginAsync()) {
                    _logger.LogError("登录失败，无法继续解密文件");
                    return;
                }
            }
            // else {
            //     logger.LogInformation($"复用ID登录成功! LoginID:{_loginId}---LoginTime:{_loginTime}");
            //
            // }

            string? currentLoginId;
            lock (_loginLock) {
                currentLoginId = _loginId;
            }

            if (string.IsNullOrEmpty(currentLoginId)) {
                _logger.LogError("登录ID无效");
                return;
            }

            try {
                var decryptRequest = new IpgJsonDecryptFileRequest {
                    LoginID = currentLoginId,
                    Param = new IpgJsonDecryptFileRequestParam() {
                        Files = filesToDecrypt
                    }
                };

                string jsonRequest = JsonSerializer.Serialize(decryptRequest);
                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                // 发送解密请求
                var response = await _httpClient.PostAsync(
                    _ipgJsonSettings.IpgJsonUrl + "callfunction",
                    content
                );

                string jsonResponse = await response.Content.ReadAsStringAsync();
                var decryptResponse = JsonSerializer.Deserialize<IpgJsonResponse>(jsonResponse);

                if (decryptResponse is not null) {
                    if (decryptResponse.error == "0") {
                        _logger.LogInformation("文件解密成功!");
                        return; // 成功完成，直接返回
                    }

                    if (decryptResponse.error == "61453") { // 登录过期
                        _logger.LogWarning("登录已过期，正在尝试重新登录...");
                        await LogoutAsync(); // 先登出
                        shouldRetry = retryCount < maxRetryCount; // 检查是否还可以重试
                        if (shouldRetry) {
                            retryCount++;
                            _logger.LogInformation($"正在重试第 {retryCount} 次...");
                        }
                    }
                    else {
                        _logger.LogWarning($"文件解密失败, 错误: {decryptResponse.desc}");
                        return; // 其他错误直接返回
                    }
                }
            }
            catch (Exception ex) {
                _logger.LogError($"解密过程中发生异常：{ex.Message}");
                if (retryCount < maxRetryCount) {
                    shouldRetry = true;
                    retryCount++;
                    _logger.LogInformation($"发生异常，正在重试第 {retryCount} 次...");
                }
                else {
                    throw; // 重试次数用尽，抛出异常
                }
            }
        } while (shouldRetry);

        _logger.LogError($"解密失败，已达到最大重试次数 {maxRetryCount + 1} 次");
    }
}