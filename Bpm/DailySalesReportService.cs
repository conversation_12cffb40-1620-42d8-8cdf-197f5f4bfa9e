using Microsoft.Extensions.Options;
using SqlSugar;
using SuntechApp.ITS;
using System.Data;
using System.Globalization;
using System.Text;

namespace SuntechApp.Bpm;

public class DailySalesReportService(
    ILogger<DailySalesReportService> logger,
    IOptions<RptSettings> rptSettings,
    ISqlSugarClient db,
    ITSEmailServices iTSEmailServices) {
    private readonly ILogger<DailySalesReportService> _logger = logger;
    private readonly RptSettings _rptSettings = rptSettings.Value;
    private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("RPT");
    private readonly ITSEmailServices _iTSEmailServices = iTSEmailServices;

    public class SalesReportItem {
        public string BusinessUnit { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string NewOrders { get; set; } = string.Empty;
        public string Inbound { get; set; } = string.Empty;
        public string Outbound { get; set; } = string.Empty;
        public string PendingOrders { get; set; } = "----";
    }

    private async Task<List<SalesReportItem>> GenerateSalesReport() {
        var reportItems = new List<SalesReportItem>();
        try {
            var today = DateTime.Now.Date.AddDays(-1);
            var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
            var firstDayOfQuarter = new DateTime(today.Year, (today.Month - 1) / 3 * 3 + 1, 1);
            var firstDayOfYear = new DateTime(today.Year, 1, 1);

            var businessUnits = new[] { "一部", "二部", "三部", "瓷意" };
            var categories = new[] { "MTD", "QTD", "YTD" };

            // 用于存储所有数据
            var allItems = new List<SalesReportItem>();
            var summaryItems = new List<SalesReportItem>();

            // 1. 获取所有入库数据
            var fgData = await _db.Ado.UseStoredProcedure()
                .GetDataTableAsync("rptFgJobSP", new {
                    div1 = 1, // 获取所有部门数据
                    div2 = 1,
                    div3 = 1,
                    div4 = 1,
                    hf = 1,
                    cy = 1,
                    StartDate = firstDayOfYear,
                    EndDate = today
                });

            // 2. 获取所有出货数据
            var shipData = await _db.Ado.UseStoredProcedure()
                .GetDataTableAsync("rptDeliverySummarySP", new {
                    div1 = 1,
                    div2 = 1,
                    div3 = 1,
                    div4 = 1,
                    hf = 1,
                    cy = 1,
                    sd = 1,
                    hql = 1,
                    StartDate = firstDayOfYear,
                    EndDate = today
                });

            // 3. 获取所有新订单数据
            var coitemData = await _db.Ado.UseStoredProcedure()
                .GetDataTableAsync("rptCoitemSP", new {
                    div1 = 1,
                    div2 = 1,
                    div3 = 1,
                    div4 = 1,
                    hf = 1,
                    cy = 1,
                    sd = 1,
                    hql = 1,
                    StartDate = firstDayOfYear,
                    EndDate = today,
                    rpt_type = "B"
                });

            // 4. 获取所有待出货订单数据
            var coitemtoshipData = await _db.Ado.UseStoredProcedure()
                .GetDataTableAsync("rptCoitemSP", new {
                    div1 = 1,
                    div2 = 1,
                    div3 = 1,
                    div4 = 1,
                    hf = 1,
                    cy = 1,
                    sd = 1,
                    hql = 1,
                    EndDate = today,
                    rpt_type = "T"
                });


            // 处理每个类别
            foreach (var category in categories) {
                var startDate = category switch {
                    "MTD" => firstDayOfMonth,
                    "QTD" => firstDayOfQuarter,
                    "YTD" => firstDayOfYear,
                    _ => firstDayOfYear
                };

                var categoryItems = new List<SalesReportItem>();

                // 处理每个事业部
                foreach (var unit in businessUnits) {
                    try {
                        // 从内存中筛选数据
                        var unitFilter = GetUnitFilter(unit);

                        // 筛选入库数据
                        var fgRows = fgData.AsEnumerable()
                            .Where(r => r.Field<DateTime>("trans_date") >= startDate &&
                                        r.Field<DateTime>("trans_date") < today.AddDays(1).Date &&
                                        unitFilter(r));
                        // 筛选出货数据
                        var shipRows = shipData.AsEnumerable()
                            .Where(r => r.Field<DateTime>("ship_date") >= startDate &&
                                        r.Field<DateTime>("ship_date") < today.AddDays(1).Date &&
                                        unitFilter(r));
                        // 筛选新订单数据
                        var coitemRows = coitemData.AsEnumerable()
                            .Where(r => r.Field<DateTime>("CreateDate") >= startDate &&
                                        r.Field<DateTime>("CreateDate") < today.AddDays(1).Date &&
                                        unitFilter(r));


                        // 筛选待出货订单数据
                        var coitemtoshipRows = category == "MTD"
                            ? coitemtoshipData.AsEnumerable()
                                .Where(r => unitFilter(r))
                                .ToList()
                            : [];


                        // 计算各项数据
                        var inboundAmount = fgRows.Sum(r => Convert.ToDecimal(r["price"]));
                        var inboundQty = fgRows.Sum(r => Convert.ToDecimal(r["qty"]));

                        var outboundAmount = shipRows.Sum(r => Convert.ToDecimal(r["price"]));
                        var outboundQty = shipRows.Sum(r => Convert.ToDecimal(r["qty"]));

                        var newOrdersAmount = coitemRows.Sum(r => Convert.ToDecimal(r["price_total"]));
                        var newOrdersQty = coitemRows.Sum(r => Convert.ToDecimal(r["qty_ordered"]));

                        // 添加主行
                        var item = new SalesReportItem {
                            BusinessUnit = unit,
                            Category = category,
                            NewOrders = newOrdersAmount.ToString("N0", CultureInfo.InvariantCulture),
                            Inbound = inboundAmount.ToString("N0", CultureInfo.InvariantCulture),
                            Outbound = outboundAmount.ToString("N0", CultureInfo.InvariantCulture),
                            PendingOrders = category == "MTD"
                                ? coitemtoshipRows
                                    .Where(r => r is not null)
                                    .Sum(r => {
                                        var qty = Convert.ToDecimal(r["qty_to_ship"]);
                                        var price = Convert.ToDecimal(r["unit_price"]);
                                        return qty * price;
                                    })
                                    .ToString("N0", CultureInfo.InvariantCulture)
                                : "----"
                        };
                        categoryItems.Add(item);

                        // 为二部添加数量行
                        if (unit == "二部") {
                            var qtyItem = new SalesReportItem {
                                BusinessUnit = "二部",
                                Category = $"{category}(数量)",
                                NewOrders = newOrdersQty.ToString("N0", CultureInfo.InvariantCulture),
                                Inbound = inboundQty.ToString("N0", CultureInfo.InvariantCulture),
                                Outbound = outboundQty.ToString("N0", CultureInfo.InvariantCulture),
                                PendingOrders = category == "MTD"
                                ? coitemtoshipRows
                                    .Where(r => r is not null)
                                    .Sum(r => {
                                        var qty = Convert.ToDecimal(r["qty_to_ship"]);
                                        return qty;
                                    })
                                    .ToString("N0", CultureInfo.InvariantCulture)
                                : "----"
                            };
                            categoryItems.Add(qtyItem);
                        }
                    }
                    catch (Exception ex) {
                        _logger.LogError(ex, $"处理事业部 {unit} 的 {category} 数据时出错");
                    }
                }

                // 添加汇总行
                var summaryItem = new SalesReportItem {
                    BusinessUnit = "总和",
                    Category = category,
                    NewOrders = categoryItems
                        .Where(x => !x.Category.EndsWith("(数量)"))
                        .Sum(x => decimal.Parse(x.NewOrders))
                        .ToString("N0", CultureInfo.InvariantCulture),
                    Inbound = categoryItems
                        .Where(x => !x.Category.EndsWith("(数量)"))
                        .Sum(x => decimal.Parse(x.Inbound))
                        .ToString("N0", CultureInfo.InvariantCulture),
                    Outbound = categoryItems
                        .Where(x => !x.Category.EndsWith("(数量)"))
                        .Sum(x => decimal.Parse(x.Outbound))
                        .ToString("N0", CultureInfo.InvariantCulture),
                    PendingOrders = category == "MTD"
                        ? categoryItems
                            .Where(x => x.PendingOrders != "----")
                            .Select(x => decimal.TryParse(x.PendingOrders,
                                NumberStyles.Number | NumberStyles.AllowThousands,
                                CultureInfo.InvariantCulture, out var v)
                                ? v
                                : 0)
                            .Sum()
                            .ToString("N0", CultureInfo.InvariantCulture)
                        : "----"
                };

                allItems.AddRange(categoryItems);
                summaryItems.Add(summaryItem);
            }

            // 排序逻辑保持不变
            var orderedItems = allItems
                .OrderBy(x => x.Category)
                .ThenBy(x => {
                    if (x.BusinessUnit == "二部" && x.Category.EndsWith("(数量)")) return 4;

                    if (x.BusinessUnit == "总和") return 5;

                    return x.BusinessUnit switch {
                        "一部" => 0,
                        "二部" => 1,
                        "三部" => 2,
                        "瓷意" => 3,
                        _ => 6
                    };
                })
                .ToList();

            // 重新组织数据，确保每个类别的汇总行都紧跟在该类别的最后
            var finalOrderedItems = new List<SalesReportItem>();
            var currentCategory = "";

            foreach (var item in orderedItems) {
                var category = item.Category.EndsWith("(数量)")
                    ? item.Category.Substring(0, item.Category.Length - 4)
                    : item.Category;

                if (category != currentCategory) {
                    if (!string.IsNullOrEmpty(currentCategory)) {
                        var summary = summaryItems.FirstOrDefault(s => s.Category == currentCategory);
                        if (summary != null) finalOrderedItems.Add(summary);
                    }

                    currentCategory = category;
                }

                finalOrderedItems.Add(item);
            }

            // 添加最后一个类别的汇总行
            if (!string.IsNullOrEmpty(currentCategory)) {
                var summary = summaryItems.FirstOrDefault(s => s.Category == currentCategory);
                if (summary != null && !finalOrderedItems.Contains(summary)) finalOrderedItems.Add(summary);
            }

            return finalOrderedItems;
        }
        catch (Exception ex) {
            _logger.LogError(ex, "生成销售报表时出错");
            return [];
        }
    }

    // 辅助方法：根据事业部获取筛选条件 
    private Func<DataRow, bool> GetUnitFilter(string unit) {
        return r => {
            try {
                if (!r.Table.Columns.Contains("div")) {
                    _logger.LogWarning("'div' 列不存在于数据表中");
                    return false;
                }

                var divValue = r["div"]?.ToString();
                if (string.IsNullOrEmpty(divValue)) return false;

                // 根据部门名称返回对应的 div 值
                var result = unit switch {
                    "一部" => divValue == "1", // 一部
                    "二部" => divValue == "2", // 二部
                    "三部" => divValue == "3", // 三部
                    "瓷意" => divValue == "4", // 瓷意
                    _ => false
                };

                return result;
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"处理事业部 {unit} 时出错");
                return false;
            }
        };
    }

    public async Task DailySalesReportSend() {
        try {
            // 1. 生成报表数据
            var reportData = await GenerateSalesReport();

            // 2. 获取当前日期
            var reportDate = DateTime.Now.AddDays(-1).ToString("yyyy年MM月dd日");
            var reportTitle = $"{reportDate} 销售日报";

            // 3. 将报表数据转换为HTML表格
            var EndDateP = new SugarParameter("@EndDate", DateTime.Now.AddDays(-1));
            var MainBodyP = new SugarParameter("@MainBody", null, true)
            {
                DbType = System.Data.DbType.String,
                Size = -1 // 关键点，-1代表nvarchar(max)
            };
            await _db.Ado.UseStoredProcedure().GetDataTableAsync("DailySalesReportPluginSP", EndDateP, MainBodyP);
            string MainBody = MainBodyP.Value?.ToString() ?? string.Empty;
            var htmlContent = GenerateHtmlReport(reportData) + MainBody;
            await _iTSEmailServices.Subject(reportTitle)
                .Body(htmlContent)
                .ToAddress(_rptSettings.DailySalesTo)
                .SendAsync();

            _logger.LogInformation("销售日报邮件发送成功");
        }
        catch (Exception ex) {
            _logger.LogError(ex, "发送销售日报邮件时出错");
            throw;
        }
    }

private string GenerateHtmlReport(List<SalesReportItem> reportData) {
    var sb = new StringBuilder();

    sb.AppendLine("<style>");
    sb.AppendLine("    body { font-family: Arial, sans-serif; margin: 0; padding: 8px; background-color: #f5f5f5; }");
    sb.AppendLine("    .container { width: 100%; max-width: 100%; margin: 0 auto; }");
    sb.AppendLine("    table { border-collapse: collapse; width: 100%; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1); table-layout: fixed; }");
    sb.AppendLine("    th, td { border: 1px solid #e0e0e0; text-align: right; padding: 6px 4px; overflow: hidden; text-overflow: ellipsis; }");
    sb.AppendLine("    th { background-color: #f2f2f2; text-align: center; position: sticky; top: 0; font-weight: bold; }");
    sb.AppendLine("    .summary-row { font-weight: bold; background-color: #f8f8f8; }");
    sb.AppendLine("    .text { text-align: center; }");
    
    // 桌面端样式
    sb.AppendLine("    @media (min-width: 1025px) {");
    sb.AppendLine("        .table-wrapper { width: auto; }");
    sb.AppendLine("        table { max-width: 600px; }");
    sb.AppendLine("        th:nth-child(1), td:nth-child(1) { width: 12%; }");
    sb.AppendLine("        th:nth-child(2), td:nth-child(2) { width: 18%; }");
    sb.AppendLine("        th:nth-child(3), td:nth-child(3) { width: 14%; }");
    sb.AppendLine("        th:nth-child(4), td:nth-child(4) { width: 14%; }");
    sb.AppendLine("        th:nth-child(5), td:nth-child(5) { width: 14%; }");
    sb.AppendLine("        th:nth-child(6), td:nth-child(6) { width: 14%; }");
    sb.AppendLine("    }");
    
    // 平板端样式
    sb.AppendLine("    @media (max-width: 1024px) {");
    sb.AppendLine("        body { padding: 15px; }");
    sb.AppendLine("        .table-wrapper { width: 100%; }");
    sb.AppendLine("        table { width: 100%; }");
    sb.AppendLine("        th:nth-child(1), td:nth-child(1) { width: 12%; }");
    sb.AppendLine("        th:nth-child(2), td:nth-child(2) { width: 18%; }");
    sb.AppendLine("        th:nth-child(3), td:nth-child(3) { width: 14%; }");
    sb.AppendLine("        th:nth-child(4), td:nth-child(4) { width: 14%; }");
    sb.AppendLine("        th:nth-child(5), td:nth-child(5) { width: 14%; }");
    sb.AppendLine("        th:nth-child(6), td:nth-child(6) { width: 14%; }");
    sb.AppendLine("    }");
    
    // 移动端样式
    sb.AppendLine("    @media (max-width: 768px) {");
    sb.AppendLine("        body { padding: 8px; }");
    sb.AppendLine("        th, td { padding: 6px 4px; font-size: 12px; }");
    sb.AppendLine("        th:nth-child(1), td:nth-child(1) { width: 12%; padding-left: 2px; }");
    sb.AppendLine("        th:nth-child(2), td:nth-child(2) { width: 16%; padding-left: 2px; }");
    sb.AppendLine("        th:nth-child(n+3), td:nth-child(n+3) { width: 14%; }");
        sb.AppendLine("        .number { font-size: 9px; }");
    sb.AppendLine("    }");
    
    // 超小屏幕优化
    sb.AppendLine("    @media (max-width: 480px) {");
    sb.AppendLine("        body { padding: 4px; }");
    sb.AppendLine("        th, td { padding: 4px 2px; font-size: 11px; }");
    sb.AppendLine("        th:nth-child(1), td:nth-child(1) { width: 12%; }");
    sb.AppendLine("        th:nth-child(2), td:nth-child(2) { width: 16%; }");
    sb.AppendLine("        th:nth-child(n+3), td:nth-child(n+3) { width: 14%; }");
        sb.AppendLine("        .number { font-size: 9px; }");
    sb.AppendLine("    }");
    sb.AppendLine("</style>");

    // 开始容器和表格
    sb.AppendLine("<div class=\"container\">");
    sb.AppendLine("    <div class=\"table-wrapper\">");
    sb.AppendLine("        <table>");

    // 表头
    sb.AppendLine("            <tr>");
    sb.AppendLine("                <th>事业部</th>");
    sb.AppendLine("                <th>类别</th>");
    sb.AppendLine("                <th>新订单</th>");
    sb.AppendLine("                <th>入库</th>");
    sb.AppendLine("                <th>出货</th>");
    sb.AppendLine("                <th>待出货</th>");
    sb.AppendLine("            </tr>");

    // 表格内容
    foreach (var item in reportData) {
        var isSummaryRow = item.BusinessUnit == "总和";
        sb.AppendLine($"            <tr{(isSummaryRow ? " class=\"summary-row\"" : "")}>");
        sb.AppendLine($"                <td class=\"text\">{item.BusinessUnit}</td>");
        sb.AppendLine($"                <td class=\"text\">{item.Category}</td>");
        sb.AppendLine($"                <td class=\"number\">{item.NewOrders}</td>");
        sb.AppendLine($"                <td class=\"number\">{item.Inbound}</td>");
        sb.AppendLine($"                <td class=\"number\">{item.Outbound}</td>");
        sb.AppendLine($"                <td class=\"number\">{item.PendingOrders}</td>");
        sb.AppendLine("            </tr>");
    }

    // 结束表格和容器
    sb.AppendLine("        </table>");
    sb.AppendLine("    </div>");
    sb.AppendLine("</div>");

    return sb.ToString();
}
}