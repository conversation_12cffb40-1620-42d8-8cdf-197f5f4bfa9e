@using SqlSugar
@using SuntechApp.Data
@inject ISqlSugarClient db
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudText Typo="Typo.h6" Class="mb-4">上传试卷</MudText>
        
        <MudForm @ref="uploadForm" @bind-IsValid="@isUploadFormValid">
            <MudTextField @bind-Value="newPaperTitle" 
                        Label="试卷标题" 
                        Required="true" 
                        RequiredError="请输入试卷标题"
                        Class="mb-3" />
            
            <MudAlert Severity="Severity.Info" Class="mb-3" Dense="true">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Info" Class="mr-2" />
                    <div>
                        <MudText Typo="Typo.body2">
                            点击选择文件时，浏览器可能会短暂冻结，这是正常现象，请耐心等待文件选择器打开。
                        </MudText>
                    </div>
                </div>
            </MudAlert>

            <MudFileUpload T="IBrowserFile"
                         Accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.csv,.txt"
                         FilesChanged="@OnPaperFileSelected"
                         MaximumFileCount="1">
                <ActivatorContent>
                    <MudButton Variant="Variant.Outlined"
                             Color="Color.Primary"
                             StartIcon="@Icons.Material.Filled.CloudUpload">
                        选择试卷文件
                    </MudButton>
                </ActivatorContent>
            </MudFileUpload>
            
            @if (selectedPaperFile != null)
            {
                <MudText Typo="Typo.body2" Class="mt-2 mb-3">
                    已选择文件：@selectedPaperFile.Name (@FormatFileSize(selectedPaperFile.Size))
                </MudText>
            }

            @if (uploading)
            {
                <MudCard Class="mt-3 pa-4" Style="background-color: #f5f5f5;">
                    <div class="d-flex align-center mb-3">
                        <MudIcon Icon="@Icons.Material.Filled.CloudUpload"
                                Color="Color.Primary"
                                Class="mr-2 rotating" />
                        <MudText Typo="Typo.h6">正在上传试卷...</MudText>
                    </div>
                    <MudProgressLinear Value="@uploadProgress"
                                     Color="Color.Primary"
                                     Size="Size.Medium"
                                     Class="mb-2" />
                    <div class="d-flex justify-space-between">
                        <MudText Typo="Typo.body2">@($"{uploadProgress:F0}%")</MudText>
                        <MudText Typo="Typo.body2">@uploadStatusText</MudText>
                    </div>
                </MudCard>
            }
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 OnClick="UploadPaper" 
                 Disabled="@(!isUploadFormValid || selectedPaperFile == null || uploading)">
            @if (uploading)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                <span class="ml-2">上传中...</span>
            }
            else
            {
                <span>上传</span>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .rotating {
        animation: rotate 2s linear infinite;
    }

    @@keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>

@code {
    [CascadingParameter] IMudDialogInstance MudDialog { get; set; } = null!;
    
    private SqlSugarProvider DefaultDb => db.AsTenant().GetConnection("Default");
    private bool uploading = false;
    private bool isUploadFormValid = false;
    private string newPaperTitle = string.Empty;
    private IBrowserFile? selectedPaperFile;
    private MudForm uploadForm = null!;

    // 进度条相关变量
    private double uploadProgress = 0;
    private string uploadStatusText = "";

    private void OnPaperFileSelected(IBrowserFile? file)
    {
        selectedPaperFile = file;
        StateHasChanged();
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task UploadPaper()
    {
        if (selectedPaperFile == null) return;
        
        try
        {
            uploading = true;
            uploadProgress = 0;
            uploadStatusText = "开始上传...";
            StateHasChanged();

            // 验证文件大小
            uploadProgress = 10;
            uploadStatusText = "验证文件大小...";
            StateHasChanged();
            await Task.Delay(200);

            if (selectedPaperFile.Size > 52428800) // 50MB
            {
                Snackbar.Add("文件大小不能超过50MB", Severity.Error);
                return;
            }

            // 验证文件类型
            uploadProgress = 20;
            uploadStatusText = "验证文件类型...";
            StateHasChanged();
            await Task.Delay(200);

            var allowedTypes = new[] { ".pdf", ".doc", ".docx", ".ppt", ".pptx", ".xls", ".xlsx", ".csv", ".txt" };
            var fileExtension = Path.GetExtension(selectedPaperFile.Name).ToLower();
            if (!allowedTypes.Contains(fileExtension))
            {
                Snackbar.Add("不支持的文件类型", Severity.Error);
                return;
            }

            // 创建保存目录
            uploadProgress = 30;
            uploadStatusText = "创建保存目录...";
            StateHasChanged();
            await Task.Delay(200);

            var paperPath = @"C:\ExamFiles\Papers";
            if (!Directory.Exists(paperPath))
            {
                Directory.CreateDirectory(paperPath);
            }

            // 生成唯一文件名
            uploadProgress = 40;
            uploadStatusText = "生成文件名...";
            StateHasChanged();
            await Task.Delay(200);

            var fileName = $"{Guid.NewGuid()}{fileExtension}";
            var filePath = Path.Combine(paperPath, fileName);

            // 保存文件
            uploadProgress = 50;
            uploadStatusText = "保存文件...";
            StateHasChanged();

            using var stream = new FileStream(filePath, FileMode.Create);
            var fileStream = selectedPaperFile.OpenReadStream(52428800);

            // 模拟文件上传进度
            var buffer = new byte[8192];
            var totalBytes = selectedPaperFile.Size;
            var bytesRead = 0L;
            int currentRead;

            while ((currentRead = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
            {
                await stream.WriteAsync(buffer, 0, currentRead);
                bytesRead += currentRead;

                // 更新进度 (50% - 80% 用于文件保存)
                var fileProgress = (double)bytesRead / totalBytes;
                uploadProgress = 50 + (fileProgress * 30);
                uploadStatusText = $"保存文件... {bytesRead / 1024:F0}KB / {totalBytes / 1024:F0}KB";
                StateHasChanged();

                // 小延迟让用户看到进度
                if (bytesRead % (8192 * 10) == 0) // 每80KB更新一次
                    await Task.Delay(50);
            }

            // 保存到数据库
            uploadProgress = 85;
            uploadStatusText = "保存到数据库...";
            StateHasChanged();
            await Task.Delay(300);

            var examPaper = new ExamPaper
            {
                Title = newPaperTitle,
                FileName = selectedPaperFile.Name,
                FilePath = filePath,
                UploadedDate = DateTime.Now,
                IsActive = true
            };

            await DefaultDb.Insertable(examPaper).ExecuteCommandAsync();

            // 完成
            uploadProgress = 100;
            uploadStatusText = "上传完成！";
            StateHasChanged();
            await Task.Delay(500);

            Snackbar.Add("试卷上传成功！", Severity.Success);
            MudDialog.Close(DialogResult.Ok(true));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"上传失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            uploading = false;
            uploadProgress = 0;
            uploadStatusText = "";
            StateHasChanged();
        }
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
        return $"{bytes / (1024 * 1024):F1} MB";
    }
}
