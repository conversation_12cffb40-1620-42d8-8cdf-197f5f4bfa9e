@page "/Exam"
@using SqlSugar
@using SuntechApp.Data
@inject ISqlSugarClient db
@inject ISnackbar Snackbar
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle>在线考试</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <div class="d-flex align-center mb-6">
        <MudIcon Icon="@Icons.Material.Filled.Quiz" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
        <MudText Typo="Typo.h3" Color="Color.Primary">在线考试系统</MudText>
    </div>

    <MudAlert Severity="Severity.Info" Class="mb-4">
        <div class="d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Info" Class="mr-2" />
            <div>
                <MudText Typo="Typo.body1" Class="font-weight-medium">欢迎参加在线考试</MudText>
                <MudText Typo="Typo.body2">请选择您要参加的考试，下载试卷后完成答题并提交答案文件</MudText>
            </div>
        </div>
    </MudAlert>

    <MudGrid>
        <MudItem xs="12">
            <MudPaper Class="pa-4" Elevation="3">
                <div class="d-flex align-center mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Assignment" Class="mr-2" Color="Color.Primary" />
                    <MudText Typo="Typo.h5">可用考试</MudText>
                </div>

                @if (loading) {
                    <MudProgressCircular Indeterminate="true"/>
                    <MudText Class="ml-2">加载中...</MudText>
                }
                else if (!examPapers.Any()) {
                    <MudAlert Severity="Severity.Info">
                        <MudText>当前没有可用的考试</MudText>
                    </MudAlert>
                }
                else {
                    <MudGrid>
                        @foreach (var exam in examPapers) {
                            <MudItem xs="12" sm="6" md="4">
                                <MudCard Class="mb-4" Elevation="4" Style="transition: all 0.3s ease; cursor: pointer;"
                                         @onmouseenter="@(() => {})" @onmouseleave="@(() => {})">
                                    <MudCardHeader>
                                        <CardHeaderContent>
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Assignment" Color="Color.Primary" Class="mr-2" />
                                                <MudText Typo="Typo.h6" Color="Color.Primary">@exam.Title</MudText>
                                            </div>
                                        </CardHeaderContent>
                                        <CardHeaderActions>
                                            <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                                可参加
                                            </MudChip>
                                        </CardHeaderActions>
                                    </MudCardHeader>
                                    <MudCardContent>
                                        <div class="d-flex align-center mb-2">
                                            <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" Color="Color.Info" Class="mr-2" />
                                            <MudText Typo="Typo.body2" Color="Color.Info">
                                                发布时间：@exam.UploadedDate.ToString("yyyy年MM月dd日 HH:mm")
                                            </MudText>
                                        </div>
                                        <div class="d-flex align-center mb-3">
                                            <MudIcon Icon="@Icons.Material.Filled.AttachFile" Size="Size.Small" Color="Color.Dark" Class="mr-2" />
                                            <MudText Typo="Typo.body2">
                                                试卷文件：@exam.FileName
                                            </MudText>
                                        </div>
                                        <MudDivider Class="my-3" />
                                        <div class="d-flex align-center justify-center">
                                            <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Small" Color="Color.Info" Class="mr-2" />
                                            <MudText Typo="Typo.caption" Color="Color.Info">
                                                点击下方按钮开始考试
                                            </MudText>
                                        </div>
                                    </MudCardContent>
                                    <MudCardActions Class="justify-center pa-4">
                                        <MudButton Variant="Variant.Filled"
                                                   Color="Color.Primary"
                                                   StartIcon="@Icons.Material.Filled.PlayArrow"
                                                   OnClick="@(() => StartExam(exam.Id))"
                                                   Size="Size.Large"
                                                   FullWidth="true">
                                            开始考试
                                        </MudButton>
                                    </MudCardActions>
                                </MudCard>
                            </MudItem>
                        }
                    </MudGrid>
                }
            </MudPaper>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private SqlSugarProvider DefaultDb => db.AsTenant().GetConnection("Default");
    private List<ExamPaper> examPapers = new();
    private bool loading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadExamPapers();
    }

    private async Task LoadExamPapers()
    {
        try
        {
            loading = true;
            StateHasChanged();

            examPapers = await DefaultDb.Queryable<ExamPaper>()
                .Where(x => x.IsActive)
                .OrderByDescending(x => x.UploadedDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载考试列表失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private void StartExam(int examId)
    {
        Navigation.NavigateTo($"/Exam/Take/{examId}");
    }
}
