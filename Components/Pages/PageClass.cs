using MudBlazor;
using System;

namespace SuntechApp.Components.Pages
{
    public class DialogBase(IDialogService DialogService)
    {
        private readonly IDialogService _dialogService = DialogService;
        private string _title = string.Empty;
        private string _contentText = string.Empty;
        private string _buttonText = string.Empty;
        private bool _closeButton = false;
        private bool _cancleVisible = false;
        private Color _color = MudBlazor.Color.Transparent;
        private MaxWidth _maxWidth = MudBlazor.MaxWidth.ExtraSmall;
        public DialogBase Title(string? title)
        {
            _title = title ?? string.Empty;
            return this;
        }
        public DialogBase ContentText(string? contentText)
        {
            _contentText = contentText ?? string.Empty;
            return this;
        }
        public DialogBase ButtonText(string? buttonText)
        {
            _buttonText = buttonText ?? string.Empty;
            return this;
        }
        public DialogBase CloseButton(bool? closeButton)
        {
            _closeButton = closeButton ?? false;
            return this;
        }
        public DialogBase CancleVisible(bool? cancleVisible)
        {
            _cancleVisible = cancleVisible ?? false;
            return this;
        }
        public DialogBase Color(Color? color)
        {
            _color = color ?? MudBlazor.Color.Transparent;
            return this;
        }
        public DialogBase MaxWidth(MaxWidth? maxWidth)
        {
            _maxWidth = maxWidth ?? MudBlazor.MaxWidth.ExtraSmall;
            return this;
        }
        public async Task<IDialogReference> Show()
        {
            var parameters = new DialogParameters<_Dialog>
            {
                { x => x.ContentText, _contentText },
                { x => x.ButtonText, _buttonText },
                { x => x.Color, _color },
                { x => x.CancleVisible, _cancleVisible }
            };
            var options = new DialogOptions() { CloseButton = _closeButton, MaxWidth = _maxWidth, FullWidth = true };
            return await _dialogService.ShowAsync<_Dialog>(_title, parameters, options);
        }
    }
    public class InternalTrxCustomersType
    {
        public string Display { get; set; }
        public string Type { get; set; }
    }
    public class ValueDataMap
    {
        public static List<InternalTrxCustomersType> InternalTrxCustomersTypeMap()
        {
            return
            [
            new() { Display = "全部不计", Type = "A" },
            new() { Display = "订单不计", Type = "S" }
            ];
        }
    }
}
