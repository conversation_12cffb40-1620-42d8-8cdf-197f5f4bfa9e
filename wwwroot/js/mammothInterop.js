window.mammothInterop = {
    convertDocxToHtml: async function (fileUrl, elementId) {
        const response = await fetch(fileUrl);
        const arrayBuffer = await response.arrayBuffer();
        mammoth.convertToHtml({ arrayBuffer: arrayBuffer })
            .then(function (result) {
                document.getElementById(elementId).innerHTML = result.value;
            })
            .catch(function (e) {
                document.getElementById(elementId).innerHTML = "�ĵ�����ʧ��: " + e;
            });
    }
};