@page "/EmployeeR"
@using SuntechApp.Data
@using Microsoft.EntityFrameworkCore
@inject EmployeeService EmployeeService

<MudTable Items="employees" Virtualize="true" OverscanCount="20" Hover="true" SortLabel="Sort By" Elevation="1" AllowUnsorted="false" Dense="true">
    <HeaderContent>
        <MudTh>员工ID</MudTh>
        <MudTh>姓名</MudTh>
        <MudTh>部门</MudTh>
        <MudTh>薪资</MudTh>
        <MudTh></MudTh> <!-- 新增操作列 -->
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="ID" style="width:8px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">@context.Id</MudTd>
        <MudTd DataLabel="姓名">@context.Name</MudTd>
        <MudTd DataLabel="部门">@context.Department</MudTd>
        <MudTd DataLabel="薪资">@context.Salary.ToString()</MudTd>
        <MudTd>
            <MudButton Color="Color.Error" Variant="Variant.Filled"
                       OnClick="@(() => DeleteEmployee(context))">
                删除
            </MudButton>
        </MudTd>
    </RowTemplate>
    <PagerContent>
        <MudTablePager PageSizeOptions="new int[]{10, 25, 50, 100}" />
    </PagerContent>
</MudTable>

@code {
    private List<Employee> employees = new List<Employee>();
   
    
    protected override async Task OnInitializedAsync()
    {
        employees = await EmployeeService.GetEmployeesAsync();
    }
    private async Task DeleteEmployee(Employee context)
    {
            await EmployeeService.DeleteEmployeeAsync(context.Id);
            employees = await EmployeeService.GetEmployeesAsync();
    }
}