using Hangfire;
using HtmlAgilityPack;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using MudBlazor;
using SqlSugar;
using SuntechApp.Data;
using SuntechApp.IpgPlugin;
using SuntechApp.ITS;
using System.Data;
using System.Diagnostics;
using System.Net;
using System.Net.Sockets;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace SuntechApp.Bpm {
    public interface IBpmService {
        Task RptEmailSend(string taskName);
        Task RptBpmGenerateFlow(int TaskID);
        Task RptBpmCustomerVisitRecord(int TaskID);
        Task BpmLedgerModify(int TaskID);
        Task BpmInternalWebsite(int TaskID);
        Task<string> BpmTest();
        Task PrintTextAsync(string printerIp, int port, string content);
        void PrintWithFont(string content);
        Task BpmRdWorkLog(int TaskID);
    }

    public class BpmService(
        ILogger<BpmService> logger,
        IOptions<RptSettings> rptSettings,
        IOptions<IntranetResourceSite> intranetResourceSite,
        IHttpClientFactory clientFactory,
        IDbContextFactory<SuntechAppDbContext> contextFactory,
        ISqlSugarClient db,
        ITSEmailServices iTSEmailServices,
        DecryptFilesService decryptFilesService) : IBpmService {
        private readonly RptSettings _rptSettings = rptSettings.Value;
        private readonly IntranetResourceSite _intranetResourceSite = intranetResourceSite.Value;
        private readonly ILogger<BpmService> _logger = logger;
        private readonly IHttpClientFactory _clientFactory = clientFactory;
        private readonly ITSEmailServices _iTSEmailServices = iTSEmailServices;
        private readonly IDbContextFactory<SuntechAppDbContext> _contextFactory = contextFactory;
        private readonly SqlSugarProvider _db = db.AsTenant().GetConnection("BPMDATA");
        private readonly SqlSugarProvider _dbw = db.AsTenant().GetConnection("Default");
        private readonly DecryptFilesService _decryptFilesService = decryptFilesService;
        private static readonly object _officeConvertLock = new();

        /// 发送邮件
        public async Task RptEmailSend(string taskName) {
            try {
                using var _context = _contextFactory.CreateDbContext();
                var details = await _context.TaskDetails
                    .Join(
                        _context.Tasks,
                        td => td.TaskID,
                        t => t.TaskID,
                        (td, t) => new { td, t }) // 匹配 TaskID
                    .Where(j => j.t.TaskName == taskName)
                    .Select(j => j.td) // 仅保留 TaskDetails 字段
                    .ToListAsync();
                // 过滤掉空的 URL 和收件人地址                
                foreach (var detail in details) {
                    try {
                        if (string.IsNullOrWhiteSpace(detail.url) || (string.IsNullOrWhiteSpace(detail.toaddress) &&
                                                                      string.IsNullOrWhiteSpace(detail.ccaddress)))
                            break;
                        string savepath = Path.Combine(_rptSettings.PathEmail!,
                            Guid.NewGuid().ToString() + "." + detail.ext);
                        int stat = await RptSSRS(detail.url, savepath);
                        if (stat == 1) {
                            var Attachments = new List<ITSEmailAttachment> {
                                new() {
                                    AttachmentPath = Path.Combine(_rptSettings.PathEmail!, savepath),
                                    FileName = detail.filename
                                }
                            };
                            await _iTSEmailServices.Subject(detail.subject)
                                .Body(detail.body)
                                .ToAddress(detail.toaddress)
                                .CcAddress(detail.ccaddress)
                                .Attach(Attachments)
                                .SendAsync();
                        }
                    }
                    catch (Exception ex) {
                        _logger.LogError("发送报表邮件失败: {Message}", ex.Message);
                    }
                }
            }
            catch (OperationCanceledException ex) {
                _logger.LogError("操作被取消: {Message}", ex.Message);
            }
            catch (Exception ex) {
                _logger.LogError("下载失败: {Message}", ex.Message);
            }
        }

        /// 生成SSRS文件
        [Queue("alpha")]
        public async Task RptBpmGenerateFlow(int TaskID) {
            long lenth = 0;
            string Info = "生成成功";
            string FileID = "";
            try {
                string? url;
                var dt = await _db.Ado.UseStoredProcedure()
                    .GetDataTableAsync("F_Rpt_GenerateUrlandFileNo", new { TaskID });
                if (dt != null) {
                    url = Convert.ToString(dt.Rows[0]["url"]);
                    FileID = Convert.ToString(dt.Rows[0]["FileID"]) ?? "";
                    if ((!string.IsNullOrEmpty(url)) && (!string.IsNullOrEmpty(FileID))) {
                        string savePath = Path.Combine(_rptSettings.BpmAttPath!, FileID[..4], FileID.Substring(4, 4),
                            FileID[8..]);
                        if (!File.Exists(savePath)) {
                            int stat = await RptSSRS(url, savePath);
                            if (stat == 1 && File.Exists(savePath)) {
                                FileInfo fileInfo = new(savePath);
                                lenth = fileInfo.Length;
                            }
                            else {
                                Info = "SSRS服务调用失败或文件存储失败,请联系IT部处理。";
                            }
                        }
                        else {
                            Info = "FileID所指示的文件在BPM文件系统中已存在,无法覆盖,请联系IT部处理。";
                        }
                    }
                    else {
                        Info = "获取url或FileID失败,请联系IT部处理。";
                    }
                }
            }
            catch (Exception ex) {
                _logger.LogError("报表生成失败: {Message}", ex.Message);
                Info = "生成失败: " + ex.Message;
            }

            try {
                await _db.Ado.UseStoredProcedure().GetDataTableAsync("F_AddAttachment",
                    new { FileID, Size = lenth, TaskID, Info, Type = 1 });
            }
            catch (Exception ex) {
                _logger.LogError("报表生成流程附件存储过程执行失败: {Message}", ex.Message);
            }
        }

        public async Task RptBpmCustomerVisitRecord(int TaskID) {
            short i;
            for (i = 0; i <= 3; i++) {
                try {
                    BpmNextkeys bpmNextkeys = await _db.Queryable<BpmNextkeys>()
                        .Where(it => it.KeyPrefix == "Attch" && it.SubKey == _rptSettings.CustomerVisitRecordPath)
                        .FirstAsync();
                    if (bpmNextkeys.KeyID == null)
                        continue;
                    string url = _rptSettings.CustomerVisitRecordUrl + Convert.ToString(TaskID);
                    string path = Path.Combine(_rptSettings.BpmAttPath!, _rptSettings.CustomerVisitRecordPath![..4],
                        _rptSettings.CustomerVisitRecordPath![4..],
                        bpmNextkeys.KeyID.ToString()!.Length < 4
                            ? bpmNextkeys.KeyID.ToString()!.PadLeft(4, '0')
                            : bpmNextkeys.KeyID.ToString()!);
                    bpmNextkeys.KeyID++;
                    await _db.Updateable(bpmNextkeys).Where(it => it.Id == bpmNextkeys.Id).ExecuteCommandAsync();
                    if (!File.Exists(path)) {
                        int stat = await RptSSRS(url, path);
                        if (stat != 1 || (!File.Exists(path)))
                            continue;
                        FileInfo fileInfo = new(path);
                        long lenth = fileInfo.Length;
                        await _db.Ado.UseStoredProcedure().GetDataTableAsync("F_AddAttachment",
                            new {
                                FileID = _rptSettings.CustomerVisitRecordPath! + Path.GetFileName(path), Size = lenth,
                                TaskID, Type = 2
                            });
                        break;
                    }
                }
                catch (Exception ex) {
                    _logger.LogWarning("客户活动记录程序运行出错: {Message}", ex.Message);
                }
            }
        }

        public async Task BpmLedgerModify(int TaskID) {
            try {
                await _db.Ado.UseStoredProcedure().GetDataTableAsync("F_ledger_modifySP", new { TaskID });
            }
            catch (Exception ex) {
                _logger.LogWarning("客户拜访记录程序运行出错: {Message}", ex.Message);
            }
        }

        public async Task BpmInternalWebsite(int TaskID) {
            try {
                BpmInternalWebsite bpmInternalWebsite = await _db.Queryable<BpmInternalWebsite>()
                    .Where(it => it.TaskID == TaskID)
                    .FirstAsync();

                if (bpmInternalWebsite.Type == "4") {
                    List<BpmInternalWebsiteTrain> bpmInternalWebsiteTrain = await _db
                        .Queryable<BpmInternalWebsiteTrain>()
                        .Where(it => it.TaskID == TaskID)
                        .ToListAsync();
                    string[] officeExtensions = [".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"];
                    string[] epubExtensions = [".epub"];
                    foreach (var biwt in bpmInternalWebsiteTrain) {
                        YZAppAttachment yZAppAttachment = new();
                        TrainingMaterial trainingMaterial = new();
                        if ((biwt.Attach ?? "") == "" && biwt.TmId != null) {
                            trainingMaterial = await _dbw.Queryable<TrainingMaterial>()
                                .Where(t => t.Id == biwt.TmId)
                                .OrderBy(t => t.FileID, OrderByType.Desc)
                                .FirstAsync();
                            if (trainingMaterial != null) {
                                string pathOld = Path.Combine(_intranetResourceSite.TrainingMaterialPath!,
                                    trainingMaterial.FileID![..4], trainingMaterial.FileID![4..]);
                                if (File.Exists(pathOld))
                                    File.Delete(pathOld);
                                await _dbw.Deleteable<TrainingMaterial>()
                                    .Where(t => t.Id == trainingMaterial.Id)
                                    .ExecuteCommandAsync();
                            }
                        }
                        else if (biwt.TmId != null) {
                            trainingMaterial = await _dbw.Queryable<TrainingMaterial>()
                                .Where(t => t.Id == biwt.TmId)
                                .FirstAsync();
                            if (trainingMaterial == null)
                                continue;
                            trainingMaterial.ModifiedDate = DateTime.Now;
                            trainingMaterial.Title = biwt.Title ?? "";
                            trainingMaterial.Slug = biwt.Slug ?? "";
                            trainingMaterial.ParentId = biwt.ParentId;
                            if ((trainingMaterial.BpmFileID ?? "") != biwt.Attach) {
                                yZAppAttachment = await _db.Queryable<YZAppAttachment>()
                                    .Where(t => t.FileID == biwt.Attach)
                                    .FirstAsync();
                                string[] path = await GetTrainingMaterialPath();
                                string pathOld = Path.Combine(_intranetResourceSite.TrainingMaterialPath!,
                                    trainingMaterial.FileID![..4], trainingMaterial.FileID![4..]);
                                if (File.Exists(pathOld))
                                    File.Delete(pathOld);
                                string savepath = path[0] + yZAppAttachment.Ext;
                                File.Copy(
                                    Path.Combine(_rptSettings.BpmAttPath!, yZAppAttachment.FileID[..4],
                                        yZAppAttachment.FileID.Substring(4, 4), yZAppAttachment.FileID[8..]), savepath,
                                    true);
                                // 解密文件
                                await _decryptFilesService.DecryptFilesFromPath([savepath]);
                                if (officeExtensions.Contains(Path.GetExtension(savepath),
                                        StringComparer.InvariantCultureIgnoreCase) ||
                                    epubExtensions.Contains(Path.GetExtension(savepath),
                                        StringComparer.InvariantCultureIgnoreCase)) {
                                    if (epubExtensions.Contains(Path.GetExtension(savepath),
                                            StringComparer.InvariantCultureIgnoreCase)) {
                                        ConvertEpubToPdf(savepath);
                                    }
                                    else {
                                        ConvertOfficeToPdf(savepath);
                                    }

                                    string pdfPath = Path.ChangeExtension(savepath, ".pdf");
                                    if (!File.Exists(pdfPath)) {
                                        throw new FileNotFoundException("转换后的PDF文件不存在", pdfPath);
                                    }

                                    File.Delete(savepath);
                                }

                                trainingMaterial.FileID = path[1] + ".pdf";
                                trainingMaterial.Author = bpmInternalWebsite.Applicant ?? "";
                                trainingMaterial.BpmFileID = biwt.Attach;
                            }

                            await _dbw.Updateable(trainingMaterial).ExecuteCommandAsync();
                        }
                        else if (biwt.TmId == null) {
                            yZAppAttachment = await _db.Queryable<YZAppAttachment>()
                                .Where(t => t.FileID == biwt.Attach)
                                .FirstAsync();
                            string[] path = await GetTrainingMaterialPath();
                            trainingMaterial = new TrainingMaterial {
                                Title = biwt.Title ?? "",
                                ParentId = biwt.ParentId,
                                Author = bpmInternalWebsite.Applicant ?? "",
                                Slug = biwt.Slug ?? "",
                                FileID = path[1] + ".pdf",
                                CreatedDate = DateTime.Now,
                                ModifiedDate = DateTime.Now,
                                BpmFileID = biwt.Attach
                            };
                            string savepath = path[0] + yZAppAttachment.Ext;
                            File.Copy(
                                Path.Combine(_rptSettings.BpmAttPath!, yZAppAttachment.FileID[..4],
                                    yZAppAttachment.FileID.Substring(4, 4), yZAppAttachment.FileID[8..]), savepath,
                                true);
                            await _decryptFilesService.DecryptFilesFromPath([savepath]);
                            if (officeExtensions.Contains(Path.GetExtension(savepath),
                                    StringComparer.InvariantCultureIgnoreCase) ||
                                epubExtensions.Contains(Path.GetExtension(savepath),
                                    StringComparer.InvariantCultureIgnoreCase)) {
                                if (epubExtensions.Contains(Path.GetExtension(savepath),
                                        StringComparer.InvariantCultureIgnoreCase)) {
                                    ConvertEpubToPdf(savepath);
                                }
                                else {
                                    ConvertOfficeToPdf(savepath);
                                }

                                string pdfPath = Path.ChangeExtension(savepath, ".pdf");
                                if (!File.Exists(pdfPath)) {
                                    throw new FileNotFoundException("转换后的PDF文件不存在", pdfPath);
                                }

                                File.Delete(savepath);
                            }

                            await _dbw.Insertable(trainingMaterial).ExecuteCommandAsync();
                        }
                    }
                }
                else if (bpmInternalWebsite.Type == "1") {
                    News news = new() {
                        Title = bpmInternalWebsite.Title,
                        Content = bpmInternalWebsite.Content,
                        PublishedDate = DateTime.Now,
                        Author = bpmInternalWebsite.Applicant,
                        CategoryID = 1,
                        Slug = "ai - future"
                    };
                    _dbw.Insertable(news).ExecuteCommand();
                }
            }
            catch (Exception ex) {
                _logger.LogWarning("内部网站内容发布出错: {Message}", ex.Message);
            }
        }

        /// 下载SSRS文件
        private async Task<int> RptSSRS(string url, string savePath) {
            try {
                using var client = _clientFactory.CreateClient("Client");
                if (client is HttpClient httpClient) {
                    if (httpClient.DefaultRequestVersion.Major >= 2) {
                        httpClient.DefaultVersionPolicy = HttpVersionPolicy.RequestVersionOrHigher;
                    }
                }

                string directory = Path.GetDirectoryName(savePath)!;
                if (!Directory.Exists(directory)) {
                    Directory.CreateDirectory(directory);
                }

                // 添加请求头防止缓存问题
                using var request = new HttpRequestMessage(HttpMethod.Get, url) {
                    Headers = {
                        { "Cache-Control", "no-cache" },
                        { "Pragma", "no-cache" }
                    }
                };
                // 使用异步流处理
                using var response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead,
                    CancellationToken.None);
                if (!response.IsSuccessStatusCode) {
                    string errorContent = await response.Content.ReadAsStringAsync();
                    var htmlDoc = new HtmlDocument();
                    htmlDoc.LoadHtml(errorContent);
                    var bodyNode = htmlDoc.DocumentNode.SelectSingleNode("//body");
                    if (bodyNode != null) {
                        string bodyText = Regex.Replace(bodyNode.InnerText, @"\s+", ""); // 提取所有文本内容
                        _logger.LogError("SSRS返回错误: {ErrorContent}", bodyText);
                        throw new Exception($"{bodyText}");
                    }
                }

                // 分块下载处理大文件
                const int bufferLength = 8192;
                using var contentStream = await response.Content.ReadAsStreamAsync();
                using var fileStream = new FileStream(savePath, FileMode.Create,
                    FileAccess.Write, FileShare.None, bufferLength, true);

                await contentStream.CopyToAsync(fileStream, bufferLength);
                return 1;
            }
            catch (HttpRequestException ex) when (ex.StatusCode is HttpStatusCode.RequestTimeout
                                                      or HttpStatusCode.GatewayTimeout) {
                _logger.LogError("HTTP请求超时: {Message}", ex.Message);
                return 0;
            }
            catch (IOException ex) {
                _logger.LogError("文件写入异常: {Message}", ex.Message);
                return 0;
            }
            catch (Exception ex) {
                throw new Exception(ex.Message);
            }
        }
        // 获取培训材料存储路径
        private async Task<string[]> GetTrainingMaterialPath()
        {
            var result = await _dbw.Ado.UseTranAsync(async () =>
            {
                // 1. 查询并锁定 KeyPrefix=TrainingM 的记录
                var nextKey = await _dbw.Queryable<Nextkeys>()
                    .AS("Nextkeys WITH (XLOCK, ROWLOCK)")
                    .Where(x => x.KeyPrefix == "TrainingM")
                    .FirstAsync();

                if (nextKey == null || nextKey.KeyID == null)
                    throw new Exception("Nextkeys 表中未找到 KeyPrefix=TrainingM 的记录，或 KeyID 为空");

                int keyId = nextKey.KeyID.Value;

                nextKey.KeyID = keyId + 1;
                await _dbw.Updateable(nextKey).Where(x => x.Id == nextKey.Id).ExecuteCommandAsync();
                return keyId;
             });
            if (!result.IsSuccess)
            {
                throw new Exception("事务执行失败: " + result.ErrorMessage);
            }
            int keyId = result.Data;
            // 4. 生成路径
            string path1 = (keyId / 10000 + 1).ToString("0000");
            string path2 = (keyId % 10000).ToString("0000");
            string savePath = Path.Combine(_intranetResourceSite.TrainingMaterialPath!, path1, path2);

            if (!Directory.Exists(Path.GetDirectoryName(savePath)))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(savePath)!);
            }
            return [savePath, path1 + path2];
        }

        private void ConvertOfficeToPdf(string inputFile) {
            lock (_officeConvertLock) {
                string libreOfficePath = _intranetResourceSite.LibreOfficePath ??
                                         throw new ArgumentNullException("LibreOffice path is not configured.");
                string? outputDir = Path.GetDirectoryName(inputFile);
                if (string.IsNullOrEmpty(outputDir) || !Directory.Exists(outputDir)) {
                    throw new DirectoryNotFoundException($"Output directory '{outputDir}' does not exist.");
                }

                var process = new Process();
                process.StartInfo.FileName = libreOfficePath;
                process.StartInfo.Arguments = $"--headless --convert-to pdf --outdir {outputDir} {inputFile}";
                //压缩转换质量
                // process.StartInfo.Arguments = $"--headless --convert-to pdf:writer_pdf_Export:Quality=85,ReduceImageResolution=true,MaxImageResolution=150 --outdir {outputDir} {inputFile}";
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                process.StartInfo.WorkingDirectory = Path.GetDirectoryName(libreOfficePath);

                process.Start();
                process.WaitForExit();
            }
        }

        private void ConvertEpubToPdf(string inputFile) {
            string calibrePath = _intranetResourceSite.CalibrePath ??
                                 throw new ArgumentNullException("Calibre path is not configured.");
            string outputFile = Path.ChangeExtension(inputFile, ".pdf");
            var process = new Process();
            process.StartInfo.FileName = calibrePath;
            process.StartInfo.Arguments = $"\"{inputFile}\" \"{outputFile}\"";
            process.StartInfo.UseShellExecute = false;
            process.Start();
            process.WaitForExit();
        }

        public async Task<string> BpmTest() {
            string UserAccount = "it";
            string AppId = "SD";
            string AppKey = "5EE3987F-AD09-440D-8D2B-2CAB24184D86";

            string ts = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
            string token = "";

            var paramValues = new List<string> {
                ts,
                UserAccount,
                AppId,
                AppKey
            };

            paramValues.Sort(StringComparer.Ordinal);

            string signString = string.Join(",", paramValues);

            using (MD5 md5 = MD5.Create()) {
                byte[] inputBytes = Encoding.UTF8.GetBytes(signString);
                byte[] hashBytes = md5.ComputeHash(inputBytes);

                string finalToken = BitConverter
                    .ToString(hashBytes)
                    .Replace("-", "")
                    .ToLower();
                token = finalToken;
            }

            string url = "http://**********/BPM/YZSoft/ApiService/YZService.ashx?Method=PostTask&UserAccount=" +
                         UserAccount + "&ts=" + ts + "&AppId=" + AppId + "&token=" + token;
            return url;
        }

        public async Task PrintTextAsync(string printerIp, int port, string content) {
            using var client = new TcpClient();
            await client.ConnectAsync(printerIp, port);
            using var stream = client.GetStream();

            // 进入中文模式（部分打印机支持，具体指令请查手册）
            byte[] enterChineseMode = [0x1C, 0x26]; // FS &
            // 退出中文模式
            byte[] exitChineseMode = [0x1C, 0x2E]; // FS .

            // 发送到打印机
            await stream.WriteAsync(enterChineseMode, 0, enterChineseMode.Length);
            // 发送中文内容（用GB18030编码）
            byte[] data = Encoding.GetEncoding("GB18030").GetBytes(content + "\n\f");
            await stream.WriteAsync(data, 0, data.Length);
            // 退出中文模式
            await stream.WriteAsync(exitChineseMode, 0, exitChineseMode.Length);

            await stream.FlushAsync();

            // 走纸并切纸
            //byte[] cut = new byte[] { 0x1D, 0x56, 0x00 }; // 全切纸
            //byte[] data = Encoding.UTF8.GetBytes(content + "\n");
            //await stream.WriteAsync(data, 0, data.Length);
            //await stream.WriteAsync(cut, 0, cut.Length);
        }

        public void PrintWithFont(string filePath) {
            //string printerName = "EPSON L4260 Series (已重定向 2)";
            //using var document = PdfDocument.Load(filePath);
            //using var printDocument = document.CreatePrintDocument();
            //if (!string.IsNullOrEmpty(printerName))
            //{
            //    printDocument.PrinterSettings = new PrinterSettings
            //    {
            //        PrinterName = printerName
            //    };
            //}
            //printDocument.PrintController = new StandardPrintController();
            //printDocument.Print();
        }

        public async Task BpmRdWorkLog(int TaskID) {
            try {
                // 获取特定 TaskID 的所有记录
                var workLogs = await _db.Queryable<RDWorkLogRegistrationDetail>()
                    .Where(x => x.TaskID == TaskID)
                    .ToListAsync();

                if (workLogs.Count != 0) {
                    // 按 AcctUnit3 分组计算总工时，过滤掉 WorkHours 为 null 或 0 的记录
                    var groupedWorkLogs = workLogs
                        .Where(x => x.WorkHours is not null && x.WorkHours > 0)
                        .GroupBy(x => new { x.TaskID, x.AcctUnit3 })
                        .Select(g => new {
                            g.Key.TaskID,
                            g.Key.AcctUnit3,
                            TotalHours = g.Sum(x => x.WorkHours) ?? 0,
                            FirstRecord = g.First() // 用于获取其他字段
                        })
                        .ToList();

                    foreach (var group in groupedWorkLogs) {
                        // 检查是否已存在该 TaskID 和 AcctUnit3 的记录
                        var existingRecord = await _db.Queryable<RDWorkLogRegistrationDetailClass>()
                            .Where(x => x.TaskID == group.TaskID && x.AcctUnit3 == group.AcctUnit3)
                            .FirstAsync();

                        if (existingRecord != null) {
                            // 更新现有记录
                            existingRecord.WorkHoursTotal = group.TotalHours;
                            await _db.Updateable(existingRecord).ExecuteCommandAsync();
                        }
                        else {
                            // 创建新记录
                            var newRecord = new RDWorkLogRegistrationDetailClass {
                                TaskID = group.TaskID,
                                AcctUnit3 = group.AcctUnit3,
                                DescriptionUnit3 = group.FirstRecord.DescriptionUnit3,
                                WorkHoursTotal = group.TotalHours
                            };
                            await _db.Insertable(newRecord).ExecuteCommandAsync();
                        }
                    }
                }
            }
            catch (Exception ex) {
                _logger.LogError("Error in BpmRdWorkLog: {ExMessage}", ex.Message);
            }
        }
    }
}