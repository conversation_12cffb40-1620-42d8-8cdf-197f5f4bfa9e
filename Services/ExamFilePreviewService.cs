using Microsoft.Extensions.Options;
using SuntechApp.Data;
using System.Diagnostics;
using System.Text;

namespace SuntechApp.Services
{
    public interface IExamFilePreviewService
    {
        Task<ExamFilePreviewResult> GetPreviewAsync(string filePath);
        Task<string> GetTextContentAsync(string filePath);
        Task<List<Dictionary<string, object>>> GetCsvDataAsync(string filePath);
    }

    public class ExamFilePreviewService : IExamFilePreviewService
    {
        private readonly ILogger<ExamFilePreviewService> _logger;
        private readonly IntranetResourceSite _intranetResourceSite;
        private readonly string _previewCachePath;
        private static readonly object _officeConvertLock = new();

        // 支持的Office文档扩展名
        private static readonly string[] OfficeExtensions = [".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"];
        
        // 支持的文本文件扩展名
        private static readonly string[] TextExtensions = [".txt", ".csv"];
        
        // 支持的图片文件扩展名
        private static readonly string[] ImageExtensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"];

        public ExamFilePreviewService(
            ILogger<ExamFilePreviewService> logger,
            IOptions<IntranetResourceSite> intranetResourceSite,
            IConfiguration configuration)
        {
            _logger = logger;
            _intranetResourceSite = intranetResourceSite.Value;
            
            // 创建预览缓存目录
            var submissionPath = configuration["ExamSettings:SubmissionStoragePath"] ?? @"C:\ExamFiles\Submissions";
            _previewCachePath = Path.Combine(submissionPath, "PreviewCache");
            
            if (!Directory.Exists(_previewCachePath))
            {
                Directory.CreateDirectory(_previewCachePath);
            }
        }

        public async Task<ExamFilePreviewResult> GetPreviewAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return ExamFilePreviewResult.NotFound("文件不存在");
                }

                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var fileName = Path.GetFileName(filePath);

                // PDF文件直接返回
                if (extension == ".pdf")
                {
                    return ExamFilePreviewResult.Success("pdf", filePath);
                }

                // 图片文件直接返回
                if (ImageExtensions.Contains(extension))
                {
                    return ExamFilePreviewResult.Success("image", filePath);
                }

                // 文本文件读取内容
                if (extension == ".txt")
                {
                    var content = await GetTextContentAsync(filePath);
                    return ExamFilePreviewResult.Success("text", content);
                }

                // CSV文件解析为表格数据
                if (extension == ".csv")
                {
                    var data = await GetCsvDataAsync(filePath);
                    return ExamFilePreviewResult.Success("csv", data);
                }

                // Office文档转换为PDF
                if (OfficeExtensions.Contains(extension))
                {
                    var pdfPath = await ConvertOfficeToPdfAsync(filePath);
                    if (!string.IsNullOrEmpty(pdfPath) && File.Exists(pdfPath))
                    {
                        return ExamFilePreviewResult.Success("pdf", pdfPath);
                    }
                    return ExamFilePreviewResult.Error("Office文档转换失败");
                }

                return ExamFilePreviewResult.NotSupported($"不支持预览 {extension.ToUpper()} 文件");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文件预览失败: {FilePath}", filePath);
                return ExamFilePreviewResult.Error($"预览失败: {ex.Message}");
            }
        }

        public async Task<string> GetTextContentAsync(string filePath)
        {
            try
            {
                // 尝试不同的编码方式读取文本文件
                var encodings = new[] { Encoding.UTF8, Encoding.GetEncoding("GB2312"), Encoding.Default };
                
                foreach (var encoding in encodings)
                {
                    try
                    {
                        var content = await File.ReadAllTextAsync(filePath, encoding);
                        // 检查是否包含乱码（简单检测）
                        if (!content.Contains('�'))
                        {
                            return content;
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }

                // 如果所有编码都失败，使用UTF8并返回
                return await File.ReadAllTextAsync(filePath, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取文本文件失败: {FilePath}", filePath);
                throw;
            }
        }

        public async Task<List<Dictionary<string, object>>> GetCsvDataAsync(string filePath)
        {
            try
            {
                var result = new List<Dictionary<string, object>>();
                var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
                
                if (lines.Length == 0) return result;

                // 解析CSV头部
                var headers = ParseCsvLine(lines[0]);
                
                // 解析数据行（限制前100行以避免性能问题）
                var dataLines = lines.Skip(1).Take(100);
                
                foreach (var line in dataLines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;
                    
                    var values = ParseCsvLine(line);
                    var row = new Dictionary<string, object>();
                    
                    for (int i = 0; i < headers.Length && i < values.Length; i++)
                    {
                        row[headers[i]] = values[i];
                    }
                    
                    result.Add(row);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析CSV文件失败: {FilePath}", filePath);
                throw;
            }
        }

        private async Task<string?> ConvertOfficeToPdfAsync(string inputFile)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 生成缓存文件路径
                    var fileHash = GetFileHash(inputFile);
                    var cacheFileName = $"{fileHash}.pdf";
                    var cachedPdfPath = Path.Combine(_previewCachePath, cacheFileName);

                    // 检查缓存是否存在且是最新的
                    if (File.Exists(cachedPdfPath))
                    {
                        var originalFileTime = File.GetLastWriteTime(inputFile);
                        var cachedFileTime = File.GetLastWriteTime(cachedPdfPath);
                        
                        if (cachedFileTime >= originalFileTime)
                        {
                            return cachedPdfPath;
                        }
                    }

                    // 执行转换（复用现有的转换逻辑）
                    ConvertOfficeToPdf(inputFile, _previewCachePath);
                    
                    // 检查转换结果
                    var expectedPdfPath = Path.Combine(_previewCachePath, Path.GetFileNameWithoutExtension(inputFile) + ".pdf");
                    if (File.Exists(expectedPdfPath))
                    {
                        // 重命名为缓存文件名
                        if (expectedPdfPath != cachedPdfPath)
                        {
                            File.Move(expectedPdfPath, cachedPdfPath);
                        }
                        return cachedPdfPath;
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Office文档转PDF失败: {InputFile}", inputFile);
                    return null;
                }
            });
        }

        private void ConvertOfficeToPdf(string inputFile, string outputDir)
        {
            lock (_officeConvertLock)
            {
                string libreOfficePath = _intranetResourceSite.LibreOfficePath ??
                                         throw new ArgumentNullException("LibreOffice path is not configured.");

                if (!Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                var process = new Process();
                process.StartInfo.FileName = libreOfficePath;
                process.StartInfo.Arguments = $"--headless --convert-to pdf --outdir \"{outputDir}\" \"{inputFile}\"";
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                process.StartInfo.WorkingDirectory = Path.GetDirectoryName(libreOfficePath);

                process.Start();
                
                // 设置超时时间（2分钟）
                if (!process.WaitForExit(120000))
                {
                    process.Kill();
                    throw new TimeoutException("Office文档转换超时");
                }

                if (process.ExitCode != 0)
                {
                    var error = process.StandardError.ReadToEnd();
                    throw new InvalidOperationException($"LibreOffice转换失败: {error}");
                }
            }
        }

        private static string[] ParseCsvLine(string line)
        {
            // 简单的CSV解析（不处理复杂的引号转义）
            return line.Split(',').Select(s => s.Trim().Trim('"')).ToArray();
        }

        private static string GetFileHash(string filePath)
        {
            using var md5 = System.Security.Cryptography.MD5.Create();
            var fileInfo = new FileInfo(filePath);
            var input = $"{filePath}_{fileInfo.LastWriteTime:yyyyMMddHHmmss}_{fileInfo.Length}";
            var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
            return Convert.ToHexString(hash);
        }
    }

    public class ExamFilePreviewResult
    {
        public bool Success { get; set; }
        public string Type { get; set; } = string.Empty; // pdf, image, text, csv
        public object? Data { get; set; }
        public string? ErrorMessage { get; set; }

        public static ExamFilePreviewResult Success(string type, object data)
        {
            return new ExamFilePreviewResult { Success = true, Type = type, Data = data };
        }

        public static ExamFilePreviewResult Error(string message)
        {
            return new ExamFilePreviewResult { Success = false, ErrorMessage = message };
        }

        public static ExamFilePreviewResult NotFound(string message)
        {
            return new ExamFilePreviewResult { Success = false, ErrorMessage = message };
        }

        public static ExamFilePreviewResult NotSupported(string message)
        {
            return new ExamFilePreviewResult { Success = false, ErrorMessage = message };
        }
    }
}
