@page "/ExamGrading"
@using Microsoft.AspNetCore.Authorization
@using SqlSugar
@using SuntechApp.Data
@attribute [Authorize]
@inject ISqlSugarClient db
@inject ISnackbar Snackbar
@inject IDialogService DialogService
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@rendermode InteractiveServer

<PageTitle>评卷管理</PageTitle>

    <MudPaper Class="pa-4" MaxWidth="MaxWidth.ExtraLarge" Elevation="2">
        <MudDataGrid T="ExamSubmissionView" Items="@submissions" Groupable="false" 
                     SortMode="SortMode.Multiple" Filterable="true" 
                     QuickFilter="@_quickFilter" Hover="true" Bordered="true" Dense="true">
            <ToolBarContent>
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Grading" Class="mr-2" Color="Color.Primary" />
                    <MudText Typo="Typo.h6">答案提交列表</MudText>
                    @if (selectedExamId.HasValue && availableExams.Any())
                    {
                        var selectedExam = availableExams.FirstOrDefault(e => e.Id == selectedExamId.Value);
                        <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-3">
                            当前考试：@(selectedExam?.Title ?? "未知考试")
                        </MudChip>
                    }
                </div>
                <MudSpacer />
                <div class="d-flex align-center gap-3">
                    <MudSelect T="int?"
                             Value="selectedExamId"
                             Label="选择考试"
                             Variant="Variant.Outlined"
                             Margin="Margin.Dense"
                             Style="min-width: 200px;"
                             ValueChanged="OnExamSelectionChanged">
                        <MudSelectItem T="int?" Value="null">全部考试</MudSelectItem>
                        @foreach (var exam in availableExams)
                        {
                            <MudSelectItem T="int?" Value="exam.Id">
                                @exam.Title (@exam.SubmissionCount 人提交)
                            </MudSelectItem>
                        }
                    </MudSelect>
                    <MudButton Color="Color.Success"
                             StartIcon="@Icons.Material.Filled.FileDownload"
                             OnClick="ExportCurrentExamResults"
                             Variant="Variant.Outlined"
                             Size="Size.Small"
                             Disabled="@(selectedExamId == null)">
                        导出选中考试结果
                    </MudButton>
                    <MudTextField @bind-Value="_searchString"
                                Placeholder="搜索学生姓名或工号..."
                                Adornment="Adornment.Start"
                                Immediate="true"
                                AdornmentIcon="@Icons.Material.Filled.Search"
                                IconSize="Size.Medium"
                                Class="mt-0"
                                Style="min-width: 250px;" />
                </div>
            </ToolBarContent>
            <Columns>
                <PropertyColumn Property="x => x.Id" Title="ID" />
                <PropertyColumn Property="x => x.ExamTitle" Title="考试名称" />
                <PropertyColumn Property="x => x.StudentName" Title="学生姓名" />
                <PropertyColumn Property="x => x.StudentNumber" Title="工号" />
                <TemplateColumn Title="答案文件" Sortable="false">
                    <CellTemplate>
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.AttachFile" Size="Size.Small" Class="mr-2" />
                            <MudText Typo="Typo.body2">
                                @context.Item.FileCount 个文件
                            </MudText>
                            @if (context.Item.Files.Any())
                            {
                                <MudTooltip>
                                    <ChildContent>
                                        <MudIconButton Icon="@Icons.Material.Filled.ExpandMore"
                                                     Size="Size.Small"
                                                     OnClick="@(() => ToggleFileList(context.Item.Id))" />
                                    </ChildContent>
                                    <TooltipContent>
                                        <div>
                                            @foreach (var file in context.Item.Files.Take(3))
                                            {
                                                <div>@file.FileName</div>
                                            }
                                            @if (context.Item.Files.Count > 3)
                                            {
                                                <div>... 还有 @(context.Item.Files.Count - 3) 个文件</div>
                                            }
                                        </div>
                                    </TooltipContent>
                                </MudTooltip>
                            }
                        </div>
                        @if (expandedSubmissions.Contains(context.Item.Id))
                        {
                            <div class="mt-2 ml-4">
                                @foreach (var file in context.Item.Files)
                                {
                                    <div class="d-flex align-center mb-1">
                                        <MudIcon Icon="@GetFileIcon(file.FileType)" Size="Size.Small" Class="mr-2" />
                                        <MudText Typo="Typo.caption" Class="mr-2">@file.FileName</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">(@FormatFileSize(file.FileSize))</MudText>
                                    </div>
                                }
                            </div>
                        }
                    </CellTemplate>
                </TemplateColumn>
                <PropertyColumn Property="x => x.SubmittedDate" Title="提交时间" Format="yyyy-MM-dd HH:mm" />
                <PropertyColumn Property="x => x.Score" Title="分数">
                    <CellTemplate>
                        @if (context.Item.Score.HasValue)
                        {
                            <MudChip Color="Color.Success" Size="Size.Small">@context.Item.Score.Value.ToString("F1")</MudChip>
                        }
                        else
                        {
                            <MudChip Color="Color.Default" Size="Size.Small">未评分</MudChip>
                        }
                    </CellTemplate>
                </PropertyColumn>
                <TemplateColumn Title="操作" Sortable="false">
                    <CellTemplate>
                        <div class="d-flex gap-2">
                            <MudTooltip Text="预览答案">
                                <MudIconButton Icon="@Icons.Material.Filled.Preview"
                                             Color="Color.Info"
                                             Size="Size.Small"
                                             OnClick="() => PreviewAnswer(context.Item)" />
                            </MudTooltip>
                            <MudTooltip Text="下载答案">
                                <MudIconButton Icon="@Icons.Material.Filled.Download"
                                             Color="Color.Primary"
                                             Size="Size.Small"
                                             OnClick="() => DownloadAnswer(context.Item)" />
                            </MudTooltip>
                            <MudTooltip Text="评分">
                                <MudIconButton Icon="@Icons.Material.Filled.Grading"
                                             Color="Color.Success"
                                             Size="Size.Small"
                                             OnClick="() => ShowGradingDialog(context.Item)" />
                            </MudTooltip>
                        </div>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
            <PagerContent>
                <MudDataGridPager PageSizeOptions="new int[]{10, 25, 50, 100}" />
            </PagerContent>
        </MudDataGrid>
    </MudPaper>



@code {
    private SqlSugarProvider DefaultDb => db.AsTenant().GetConnection("Default");
    private List<ExamSubmissionView> submissions = new();
    private List<ExamSubmissionView> allSubmissions = new(); // 存储所有提交数据
    private List<ExamOption> availableExams = new(); // 可选择的考试列表
    private string _searchString = "";
    private int? selectedExamId = null;
    private HashSet<int> expandedSubmissions = new(); // 展开的提交记录ID
    


    protected override async Task OnInitializedAsync()
    {
        await LoadSubmissions();
    }

    private Func<ExamSubmissionView, bool> _quickFilter => x =>
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        if (x.StudentName.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if (x.StudentNumber.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if (x.ExamTitle.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        return false;
    };

    private async Task LoadSubmissions()
    {
        try
        {
            // 加载所有提交数据
            allSubmissions = await DefaultDb.Queryable<ExamSubmission>()
                .LeftJoin<ExamPaper>((s, p) => s.ExamPaperId == p.Id)
                .Select((s, p) => new ExamSubmissionView
                {
                    Id = s.Id,
                    ExamPaperId = s.ExamPaperId,
                    ExamTitle = p.Title,
                    StudentName = s.StudentName,
                    StudentNumber = s.StudentNumber,
                    SubmittedDate = s.SubmittedDate,
                    Score = s.Score,
                    Comments = s.Comments,
                    GradedBy = s.GradedBy
                })
                .MergeTable()
                .OrderByDescending(s => s.SubmittedDate)
                .ToListAsync();

            // 批量加载文件信息
            var submissionIds = allSubmissions.Select(s => s.Id).ToList();
            if (submissionIds.Any())
            {
                var files = await DefaultDb.Queryable<ExamSubmissionFile>()
                    .Where(f => submissionIds.Contains(f.SubmissionId))
                    .OrderBy(f => f.SubmissionId)
                    .OrderBy(f => f.DisplayOrder)
                    .ToListAsync();

                // 组装文件数据
                foreach (var submission in allSubmissions)
                {
                    submission.Files = files
                        .Where(f => f.SubmissionId == submission.Id)
                        .Select(f => new ExamSubmissionFileView
                        {
                            Id = f.Id,
                            FileName = f.FileName,
                            FilePath = f.FilePath,
                            FileSize = f.FileSize,
                            FileType = f.FileType ?? "",
                            DisplayOrder = f.DisplayOrder
                        })
                        .ToList();
                }
            }

            // 生成可选择的考试列表
            availableExams = allSubmissions
                .GroupBy(s => new { s.ExamPaperId, s.ExamTitle })
                .Select(g => new ExamOption
                {
                    Id = g.Key.ExamPaperId,
                    Title = g.Key.ExamTitle,
                    SubmissionCount = g.Count()
                })
                .OrderBy(e => e.Title)
                .ToList();

            // 如果还没有选择考试，默认选择第一个
            if (selectedExamId == null && availableExams.Any())
            {
                selectedExamId = availableExams.First().Id;
            }

            // 根据选择的考试筛选提交数据
            FilterSubmissionsByExam();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载提交列表失败: {ex.Message}", Severity.Error);
        }
    }

    private void FilterSubmissionsByExam()
    {
        if (selectedExamId.HasValue)
        {
            submissions = allSubmissions.Where(s => s.ExamPaperId == selectedExamId.Value).ToList();
        }
        else
        {
            submissions = allSubmissions.ToList();
        }
        StateHasChanged();
    }

    private async Task OnExamSelectionChanged(int? examId)
    {
        selectedExamId = examId;
        FilterSubmissionsByExam();
    }

    private async Task DownloadAnswer(ExamSubmissionView submission)
    {
        try
        {
            if (submission.Files?.Any() == true)
            {
                foreach (var file in submission.Files)
                {
                    var url = $"/api/ExamFile/download-answer/{Path.GetFileName(file.FilePath)}";
                    await JSRuntime.InvokeVoidAsync("open", url, "_blank");
                    await Task.Delay(500); // 避免同时下载太多文件
                }
                Snackbar.Add($"开始下载 {submission.Files.Count} 个文件，请查看浏览器下载内容", Severity.Success);
            }
            else
            {
                Snackbar.Add("该提交没有关联的文件", Severity.Warning);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"下载失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task PreviewAnswer(ExamSubmissionView submission)
    {
        var parameters = new DialogParameters<MultiFileAnswerPreviewDialog>
        {
            { nameof(MultiFileAnswerPreviewDialog.Submission), submission }
        };

        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.ExtraLarge,
            FullWidth = true,
            CloseButton = true,
            FullScreen = false
        };

        var dialog = await DialogService.ShowAsync<MultiFileAnswerPreviewDialog>("答案预览", parameters, options);
        var result = await dialog.Result;

        // 如果用户在预览对话框中点击了"开始评分"
        if (!result.Canceled && result.Data?.ToString() == "start_grading")
        {
            await ShowGradingDialog(submission);
        }
    }

    private async Task ShowGradingDialog(ExamSubmissionView submission)
    {
        var parameters = new DialogParameters<GradingDialog>
        {
            { nameof(GradingDialog.Submission), submission }
        };

        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.Medium,
            FullWidth = true,
            CloseButton = true
        };

        var dialog = await DialogService.ShowAsync<GradingDialog>("评分", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadSubmissions();
        }
    }

    private async Task ExportCurrentExamResults()
    {
        if (selectedExamId == null)
        {
            Snackbar.Add("请先选择一个考试", Severity.Warning);
            return;
        }

        try
        {
            var selectedExam = availableExams.FirstOrDefault(e => e.Id == selectedExamId.Value);
            var examTitle = selectedExam?.Title ?? "未知考试";

            var url = $"/api/ExamFile/export-exam-results?examId={selectedExamId}";
            await JSRuntime.InvokeVoidAsync("open", url, "_blank");
            Snackbar.Add($"正在导出《{examTitle}》的考试结果，请查看浏览器下载内容", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"导出失败: {ex.Message}", Severity.Error);
        }
    }

    // 辅助方法
    private void ToggleFileList(int submissionId)
    {
        if (expandedSubmissions.Contains(submissionId))
        {
            expandedSubmissions.Remove(submissionId);
        }
        else
        {
            expandedSubmissions.Add(submissionId);
        }
        StateHasChanged();
    }

    private string GetFileIcon(string? fileType)
    {
        return fileType?.ToLower() switch
        {
            "pdf" => Icons.Material.Filled.PictureAsPdf,
            "docx" or "doc" => Icons.Material.Filled.Description,
            "xlsx" or "xls" => Icons.Material.Filled.TableChart,
            "pptx" or "ppt" => Icons.Material.Filled.Slideshow,
            "png" or "jpg" or "jpeg" or "gif" or "bmp" => Icons.Material.Filled.Image,
            "zip" or "rar" or "7z" => Icons.Material.Filled.Archive,
            "txt" => Icons.Material.Filled.TextSnippet,
            "cs" or "js" or "html" or "css" or "sql" => Icons.Material.Filled.Code,
            "mp4" or "avi" or "mov" => Icons.Material.Filled.VideoFile,
            _ => Icons.Material.Filled.AttachFile
        };
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
        return $"{bytes / (1024 * 1024):F1} MB";
    }

    // 用于显示的视图模型
    public class ExamSubmissionView
    {
        public int Id { get; set; }
        public int ExamPaperId { get; set; }
        public string ExamTitle { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public DateTime SubmittedDate { get; set; }
        public decimal? Score { get; set; }
        public string? Comments { get; set; }
        public string? GradedBy { get; set; }

        // 新增：文件列表
        public List<ExamSubmissionFileView> Files { get; set; } = new();
        public int FileCount => Files.Count;
        public string FilesSummary => $"{FileCount}个文件";
    }

    public class ExamSubmissionFileView
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string FileType { get; set; } = string.Empty;
        public int DisplayOrder { get; set; }
    }

    // 考试选项数据结构
    public class ExamOption
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public int SubmissionCount { get; set; }
    }
}
