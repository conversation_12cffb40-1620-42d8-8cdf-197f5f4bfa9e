@page "/AddBackgroundJobPage"
@using Hangfire
@using Microsoft.AspNetCore.Authorization
@using SuntechApp.IpgPlugin
@using SuntechApp.Bpm
@using System.Linq.Expressions
@attribute [Authorize]
@inject IDialogService DialogService
@inject EncryptedBPMAttFileService EncryptedBPMAttFileService
@inject DailySalesReportService DailySalesReportService
<MudCard Outlined="true" MaxWidth="600px" Margin="Margin.Auto">
    <MudCardContent>
        <MudText>增加的后台任务</MudText>
        <MudForm>
            <MudTextField Label="任务名" @bind-Value="jobName" Required></MudTextField>
            <MudSelect Label="方法名"  @bind-Value="methodName">
                @foreach (var methodName in _methodName)
                {
                    <MudSelectItem Value="methodName">@methodName</MudSelectItem>
                }
            </MudSelect>
            <MudTextField Label="Cron表达式" @bind-Value="cronExpression" Required></MudTextField>
            <MudButton Color="Color.Primary" OnClick="@SubmitJob">提交任务</MudButton>
        </MudForm>
    </MudCardContent>
</MudCard>
@code {
    private readonly IBpmService _bpmService;
    private string? jobName { get; set; }
    private string? methodName { get; set; }
    private string? cronExpression { get; set; }
    public AddBackgroundJobPage([FromKeyedServices("BpmService")] IBpmService bpmService)
    {
        _bpmService = bpmService;
    }
    private readonly string[] _methodName =
    {
        "BPM附件定期加密", "报表定时发送","每日销售邮件"
    };
    private Task SubmitJob()
    {
        try
        {
            Dictionary<string, Expression<Func<Task>>> actions = new Dictionary<string, Expression<Func<Task>>>();
            actions.Add("BPM附件定期加密", () => EncryptedBPMAttFileService.EncryptFiles());
            actions.Add("报表定时发送", () => _bpmService.RptEmailSend(jobName!));
            actions.Add("每日销售邮件", () => DailySalesReportService.DailySalesReportSend());
            var expression = actions[methodName!];
        
            RecurringJob.AddOrUpdate(jobName, expression, cronExpression,
            new RecurringJobOptions
            {
                TimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
            });
            var parameters = new DialogParameters<_Dialog>
            {
                { x => x.ContentText, "任务添加成功" },
                { x => x.ButtonText, "确定" },
                { x => x.Color, Color.Success }
            };
            var options = new DialogOptions() { MaxWidth = MaxWidth.ExtraSmall, FullWidth = true };
            return DialogService.ShowAsync<_Dialog>("成功", parameters, options);
        }
        catch (Exception ex)
        {
            var parameters = new DialogParameters<_Dialog>
            {
                { x => x.ContentText, $"任务添加失败{ex.Message}" },
                { x => x.ButtonText, "确定" },
                { x => x.Color, Color.Error }
            };
            var options = new DialogOptions() { MaxWidth = MaxWidth.ExtraSmall, FullWidth = true };
            return DialogService.ShowAsync<_Dialog>("失败", parameters, options);
        }
    }
}