using Hangfire;
using Microsoft.EntityFrameworkCore;
using SuntechApp.Bpm;
using System.ComponentModel.DataAnnotations;

namespace SuntechApp.Data
{
    public class Tasks
    {
        [Key]
        public int TaskID { get; set; }
        [StringLength(50, ErrorMessage = "名称最长50个字符")]
        public required string TaskName { get; set; }

        public string? Cron { get; set; }

        public DateTime? RecordDate { get; set; }
    }

    public class TaskDetails
    {
        [Key]
        public int id { get; set; }
        public int TaskID { get; set; }
        public int seq { get; set; }
        public string? url { get; set; }
        public string? toaddress { get; set; }
        public string? ccaddress { get; set; }
        public string? subject { get; set; }
        public string? body { get; set; }
        public string? filename { get; set; }
        public string? ext { get; set; }
    }
    public class Employee
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Department { get; set; }
        public decimal? Salary { get; set; }
    }
    public class EmployeeService(IDbContextFactory<SuntechAppDbContext> contextFactory)
    {
        private readonly IDbContextFactory<SuntechAppDbContext> _contextFactory = contextFactory;

        public async Task<List<Employee>> GetEmployeesAsync()
        {
            using var _context = _contextFactory.CreateDbContext();
            return await _context.Employees.ToListAsync();
        }

        public async Task<Employee?> GetEmployeeByIdAsync(int id)
        {
            using var _context = _contextFactory.CreateDbContext();
            return await _context.Employees.FindAsync(id);
        }

        public async Task CreateEmployeeAsync(Employee employee)
        {
            using var _context = _contextFactory.CreateDbContext();
            await _context.Employees.AddAsync(employee);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateEmployeeAsync(Employee employee)
        {
            using var _context = _contextFactory.CreateDbContext();
            _context.Employees.Update(employee);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteEmployeeAsync(int id)
        {
            using var _context = _contextFactory.CreateDbContext();
            var employee = await _context.Employees.FindAsync(id);
            if (employee != null)
            {
                _ = _context.Employees.Remove(employee);
                await _context.SaveChangesAsync();
            }
        }
    }
    public class TasksService(ILogger<TasksService> logger, IDbContextFactory<SuntechAppDbContext> contextFactory, [FromKeyedServices("BpmService")] IBpmService bpmService)
    {
        private readonly IDbContextFactory<SuntechAppDbContext> _contextFactory = contextFactory;
        private readonly IBpmService _bpmService = bpmService;
        private readonly ILogger<TasksService> _logger = logger;
        public async Task<List<Tasks>> GetTasksAsync()
        {
            using var _context = _contextFactory.CreateDbContext();
            return await _context.Tasks.ToListAsync();
        }
        public async Task UpdateTasksAsync(Tasks tasks)
        {
            using var _context = _contextFactory.CreateDbContext();
            using var transaction = _context.Database.BeginTransaction();
            try
            {
                var unchanged_tasks = await _context.Tasks.FindAsync(tasks.TaskID);
                string TaskName_unchanged = "";
                if (unchanged_tasks != null)
                {
                    TaskName_unchanged = unchanged_tasks.TaskName;
                    unchanged_tasks.RecordDate = DateTime.Now;
                    unchanged_tasks.Cron = tasks.Cron; // 如果有其他需要更新的字段也添加到这里
                    unchanged_tasks.TaskName = tasks.TaskName;
                }
                else
                {
                    await _context.Tasks.AddAsync(tasks);
                }
                await _context.SaveChangesAsync();
                RecurringJob.AddOrUpdate(tasks.TaskName, () => _bpmService.RptEmailSend(tasks.TaskName), tasks.Cron,
                new RecurringJobOptions
                {
                    TimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                });
                if (!string.IsNullOrEmpty(TaskName_unchanged) && TaskName_unchanged != tasks.TaskName)
                    RecurringJob.RemoveIfExists(TaskName_unchanged);
                transaction.Commit();
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                _logger.LogError("Tasks新建或更新出错: {Exception}", ex.ToString());
                throw;
            }
        }

        public async Task DeleteTasksAsync(int TaskID)
        {
            using var _context = _contextFactory.CreateDbContext();
            var tasks = await _context.Tasks.FindAsync(TaskID);
            if (tasks != null)
            {
                _ = _context.Tasks.Remove(tasks);
                await _context.SaveChangesAsync();
                RecurringJob.RemoveIfExists(tasks.TaskName);
            }
        }
    }
    public class InternalTrxCustomers : IEntity<int>
    {
        [Key]
        public int Id { get; set; }
        public string? CustNum { get; set; }
        public string? Name { get; set; }
        public string? Type { get; set; }
        public DateTime? CreateDate { get; set; }
        public DateTime? RecordDate { get; set; }
    }
    public class TaskDetailsService(IDbContextFactory<SuntechAppDbContext> contextFactory)
    {
        private readonly IDbContextFactory<SuntechAppDbContext> _contextFactory = contextFactory;

        public async Task<List<TaskDetails>> GetTaskDetailsAsync()
        {
            using var _context = _contextFactory.CreateDbContext();
            return await _context.TaskDetails.ToListAsync();
        }
        public async Task UpdateTaskDetailsAsync(TaskDetails taskDetails)
        {
            using var _context = _contextFactory.CreateDbContext();
            _context.TaskDetails.Update(taskDetails);
            await _context.SaveChangesAsync();
        }
        public async Task DeleteTaskDetailsAsync(int id)
        {
            using var _context = _contextFactory.CreateDbContext();
            var taskDetails = await _context.TaskDetails.FindAsync(id);
            if (taskDetails != null)
            {
                _ = _context.TaskDetails.Remove(taskDetails);
                await _context.SaveChangesAsync();
            }
        }
    }
   
    public interface IEntity<TKey>
    {
        TKey Id { get; set; }
    }
    public interface IRptRepository<TEntity, TKey>
    where TEntity : class, IEntity<TKey>  // 实体类需继承统一接口
    where TKey : IEquatable<TKey>
    {
        Task<List<TEntity>> GetDataAsync();
        Task UpdateDataAsync(TEntity entity);
        Task DeleteAsync(TKey id);
        // 其他扩展方法（如分页、条件查询）
    }
    public class RptRepository<TEntity, TKey>(IDbContextFactory<RptDbContext> contextFactory):IRptRepository<TEntity, TKey>
    where TEntity : class, IEntity<TKey>
    where TKey : IEquatable<TKey>
    {
        private readonly IDbContextFactory<RptDbContext> _contextFactory = contextFactory;

        public async Task<List<TEntity>> GetDataAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Set<TEntity>().ToListAsync();
        }
        public async Task UpdateDataAsync(TEntity entity)
        {
            using var context = _contextFactory.CreateDbContext();
            context.Set<TEntity>().Update(entity);
            await context.SaveChangesAsync();
        }
        public async Task DeleteAsync(TKey id)
        {
            using var context = _contextFactory.CreateDbContext();
            var entity = await context.Set<TEntity>().FindAsync(id);
            if (entity != null)
            {
                context.Set<TEntity>().Remove(entity);
                await context.SaveChangesAsync();
            }
        }
    }
}
