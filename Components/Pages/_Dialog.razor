<MudDialog>  
    <DialogContent>  
        <MudText>@ContentText</MudText>  
    </DialogContent>  
    <DialogActions>  
        @if (CancleVisible)  
        {  
            <MudButton OnClick="Cancel">取消</MudButton>  
        }  
        <MudButton Color="@Color" Variant="Variant.Filled" OnClick="Submit">@ButtonText</MudButton>  
    </DialogActions>  
</MudDialog>  

@code {  
    [CascadingParameter]  
    private IMudDialogInstance MudDialog { get; set; } = default!;  

    [Parameter]  
    public string ContentText { get; set; } = string.Empty;  

    [Parameter]  
    public string ButtonText { get; set; } = string.Empty;  

    [Parameter]  
    public Color Color { get; set; }  

    [Parameter]  
    public bool CancleVisible { get; set; }  

    private void Submit() => MudDialog.Close(DialogResult.Ok(true));  

    private void Cancel() => MudDialog.Cancel();  
}
