@page "/HomeTest"
@using Microsoft.AspNetCore.Components
@using System.Linq
@using SuntechApp.Data
@using SqlSugar
@inject ISqlSugarClient db

<style>
    .card-base {
        background-color: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        cursor: pointer;
    }

    .card-hover {
        transform: scale(1.05);
    }
</style>

<MudPaper Style="width:1200px">
   
        <MudText Typo="Typo.h3" Class="font-bold mb-2">欢迎回来！</MudText>
        <MudText Typo="Typo.subtitle1" Class="text-secondary mb-6">今天是 @DateTime.Now.ToString("yyyy年MM月dd日 dddd")</MudText>
        <MudGrid Spacing="4">
            @foreach (var (card, index) in cards.Select((card, index) => (card, index)))
            {
                <MudItem xs="6" md="3" lg="2">
                    <MudLink Href="@card.Href">
                        <MudCard Class="@card.CardClass"
                                 Elevation="3"
                                 HoverElevation="8"
                                 @onmouseenter="() => OnMouseEnter(index)"
                                 @onmouseleave="() => OnMouseLeave(index)">
                            <div class="rounded-lg d-flex align-items-center justify-content-center mb-3">
                            <MudIcon Icon="@card.Icon" Size="Size.Large" Class="mx-auto d-block" Color="Color.Default" />
                                @* <MudImage src="@card.ImageSrc" Width="64" Height="64" Class="mx-auto d-block" /> *@
                            </div>
                            <MudText Typo="Typo.subtitle1" Class="font-medium mx-auto d-block">@card.Title</MudText>
                        </MudCard>
                    </MudLink>
                </MudItem>
            }
        </MudGrid>
        <MudTable Items="news" Virtualize="true" OverscanCount="20" Hover="true" SortLabel="Sort By" Elevation="1" AllowUnsorted="false" Dense="true" Style="margin-top:10px;width:1200px;">
            <HeaderContent>
                <MudTh style="width:10%">发布日期</MudTh>
                <MudTh style="width:10%">作者</MudTh>
                <MudTh style="width:80%">标题</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="发布日期">@context.PublishedDate?.ToString("yyyy-MM-dd")</MudTd>
                <MudTd DataLabel="作者">@context.Author</MudTd>
                <MudTd DataLabel="标题">
                    <MudLink Href="@($"news/{context.NewsID}")">
                        <MudText Color="Color.Dark">@context.Title</MudText>
                    </MudLink>
                </MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager PageSizeOptions="new int[] { 10, 25, 50, 100 }" />
            </PagerContent>
        </MudTable>
   
        <MudCard Style="width:1200px;margin-top:10px">
                <MudText Typo="Typo.h3" GutterBottom="true" Style="text-align:center;">关于商德</MudText>
                <MudText Class="mb-8" >
                    经过多年在先进（精密）陶瓷领域的发展，商德建立了近300人的生产和销售团队以及7000平米自有厂房的现代生产基地。并于2013年通过了ISO14001和ISO9001双体系认证，给欧洲、美洲和亚洲等地客户提供精密定制工业陶瓷零件，相比海外先进陶瓷制造商，商德可以提供更具竞争力和更短时间交付的服务，相比国内先进陶瓷制造商商德可提供更高精度和更稳定产品质量保证。商德着重于陶瓷材料性能改进，持续不断通过研发的投入提高产品精度和竞争优势。
                </MudText>
        
            <MudText Typo="Typo.h3" GutterBottom="true" Style="text-align:center;">发展历程</MudText>
                <div class="d-flex align-items-center mb-4" style="width:1400px">
                    <div class="d-flex align-items-center mb-4" style="width:1200px;margin-top:20px">
                        <MudIconButton Icon="@Icons.Material.Filled.ChevronLeft" Disabled="@(_startIndex == 0)" OnClick="Prev" />
                        <MudGrid Spacing="@Spacing" Justify="Justify.Center" Class="flex-grow-1" Style="overflow:hidden;">
                            @foreach (var item in Items.Skip(_startIndex).Take(PageSize))
                            {
                                <MudItem>
                                    
                                        <div class="rounded-lg d-flex align-items-center justify-content-center mb-3">
                                        <MudPaper Height="200px" Width="200px" Class="d-flex" Style="border: 2px solid #e0e0e0;">
                                            <MudText Typo="Typo.h4" Style="text-align:center">
                                                @item.Year<br />
                                                <MudText Typo="Typo.body2" Style="text-align:left">
                                                    @foreach (var line in item.Description.Split('\n'))
                                                    {
                                                        @line
                                    
                                                        <br />
                                                    }
                                                </MudText>
                                            </MudText>
                                        </MudPaper>
                                        </div>
                                 
                                </MudItem>
                            }
                        </MudGrid>
                    <MudIconButton Icon="@Icons.Material.Filled.ChevronRight" Disabled="@(_startIndex + PageSize >= Items.Count)" OnClick="Next" />
                    </div>
                 </div>  
        <MudImage Src="images/商者无域，德行天下.jpg" Alt="图片未找到" Style="display:block;margin-left:auto;margin-right:auto" />
    </MudCard>
    <MudCard style="margin-top:5px">
        <div class="container d-flex flex-row align-items-center justify-content-center ">
            <div class="d-flex flex-column align-items-center ">
                <MudImage Src="images/logo.jpg" Alt="图片未找到" height="100" Width="160"></MudImage>
            </div>
            <div class="d-flex flex-column align-items-center me-5"style="height:100px;width:400px">
                <span class="text-muted" style="font-size:large;margin-top:20px;margin-left:10px;">
                    合肥商德应用材料有限公司
                </span>
                <span class="text-muted" style="font-size:small;margin-left:10px;">
                    地址：安徽省合肥市安徽巢湖经济开发区花山路11号
                </span>
                <span class="text-muted" style="font-size:small;margin-left:10px;">
                    电话：86-0551-88788886
                </span>
                <span class="text-muted" style="font-size:small;margin-left:10px;">
                    邮箱：<EMAIL>
                </span>
            </div>
        </div>
    </MudCard>
</MudPaper>
@code {
    public int Spacing { get; set; } = 6;
    private const int PageSize = 5;
    private int _startIndex = 0;

    private class CardState
    {
        public string Href { get; set; }
        public string Icon { get; set; }
        public string ImageSrc { get; set; }
        public string Title { get; set; }
        public bool IsHovered { get; set; }
        public string CardClass => IsHovered ? "card-base card-hover" : "card-base";
    }

    private List<News> news = new();

    protected override async Task OnInitializedAsync()
    {
        news = await db.AsTenant().QueryableWithAttr<News>()
        .Select(n => new News
        {
            NewsID = n.NewsID,
            Title = n.Title,
            Author = n.Author,
            PublishedDate = n.PublishedDate
        })
        .OrderBy(n => n.PublishedDate, OrderByType.Desc)
        .ToListAsync();
    }

    private List<CardState> cards = new List<CardState>
        {
            new CardState  
            {  
                Href = "http://erp.suntechoa.cn/WsWebClient/default.aspx",  
                Icon = @Icons.Material.Filled.FormatAlignLeft,
                Title = "ERP系统"  
            },
             new CardState
             {
                Href = "http://************:5301/login#120",
                Icon =@Icons.Material.Filled.SquareFoot,
                 Title = "IPQC系统"
            },
            new CardState
            {
                Href = "http://************/BPM/YZSoft/login/2020/?ReturnUrl=%2fbpm%2f",
                Icon =@Icons.Material.Filled.Mediation,
                Title = "BPM系统"
            },
            new CardState
            {
                Href = "https://work.weixin.qq.com/mail/",
                Icon = @Icons.Material.Filled.Email,
                Title = "腾讯企业邮箱"
            }
            ,
            new CardState
            {
                Href = "https://work.weixin.qq.com/mail/",
                Icon = @Icons.Material.Filled.Wysiwyg,
                Title = "新闻公告"
            }
            ,
            new CardState
            {
                Href = "Materials",
                Icon = @Icons.Material.Filled.MenuBook,
                Title = "培训资料"
            }
        };

    //private string CardClass = "card-base";

    private void OnMouseEnter(int index)
    {
        cards[index].IsHovered = true;
    }

    // 鼠标离开事件处理
    private void OnMouseLeave(int index)
    {
        cards[index].IsHovered = false;
    }
    // private void OnMouseEnter()
    // {
    //     CardClass = "card-base card-hover";
    // }

    // private void OnMouseLeave()
    // {
    //     CardClass = "card-base";
    // }

    private void Prev()
    {
        if (_startIndex >= PageSize)
            _startIndex -= PageSize;
    }

    private void Next()
    {
        if (_startIndex + PageSize < Items.Count)
            _startIndex += PageSize;
    }

    private List<TimelineItem> Items = new()
        {
            new TimelineItem { Year = "2022", Description = "2022.10 合肥商德特种陶瓷研发制造基地一期建成投产。\n2022.08 获评国家级专精特新“小巨人”企业。\n2022.06 获评深圳市专精特新中小企业。" },
            new TimelineItem { Year = "2021", Description = "2021.12 合肥商德特种陶瓷研发制造基地开工奠基。" },
            new TimelineItem { Year = "2020", Description = "2020.01 CAXA PLM工程管理软件成功上线。\n2020.04 成功申请研发专利累计27项、实用新型专利7项。" },
            new TimelineItem { Year = "2019", Description = "2019.05 深圳坪地分厂正式运营。\n2019.07 东方富海第二轮投资。" },
            new TimelineItem { Year = "2018", Description = "2018.01 Infor MES软件上线。\n2018.07 成功收购香港HQL公司。\n2018.10 自主研发焊线劈刀正式推出市场。" },
            new TimelineItem { Year = "2017", Description = "2017.05 通过NSF认证。\n2017.09 东方富海第一轮投资。" },
            new TimelineItem { Year = "2014", Description = "2014.03 成为ASETEK的合格供应商。\n2014.05 成功开发多孔陶瓷材料。\n2014.09 荣获国家级高新技术企业认定。\n2014.10  绿色防静电陶瓷开发成功。" },
            new TimelineItem { Year = "2013", Description = "2013.04 成为Nordson和Heraeus的合格供应商。\n2013.06 成为Makino的合格供应商。\n2013.10 Infor ERP软件上线。" },
            new TimelineItem { Year = "2012", Description = "2012.03 成为FBD的合格供应商。\n2012.06 深圳市商德电子有限公司更名为深圳市商德先进陶瓷有限公司。\n2012.09 成为HCT的合格供应商。" },
            new TimelineItem { Year = "2010", Description = "2010.08 成为大族激光的合格供应商。\n2010.09 黑色防静电陶瓷开发成功。" },
            new TimelineItem { Year = "2009", Description = "2009.04 成为ASM的合格供应商。\n2009.05 成功开发多孔陶瓷材料。" },
            new TimelineItem { Year = "2008", Description = "2008.03 成为环球仪器的合格供应商。\n2008.05 成为Kulicke&Soffa的合格供应商。\n2008.05 通过IOS9001质量管理体系认证。" },
            new TimelineItem { Year = "2006", Description = "2006.04 公司成立，注册地中国深圳，注册资金1130万。\n2006.12 成立陶瓷事业部。" }
        };

    public class TimelineItem
    {
        public string Year { get; set; }
        public string Description { get; set; }
    }
}
