window.epubInterop = {
    openEpub: function (epubUrl, containerId) {
        if (typeof ePub === "undefined") {
            var script = document.createElement("script");
            script.src = "https://cdn.jsdelivr.net/npm/epubjs/dist/epub.min.js";
            script.onload = function () {
                window.epubInterop._render(epubUrl, containerId);
            };
            document.body.appendChild(script);
        } else {
            window.epubInterop._render(epubUrl, containerId);
        }
    },
    _render: function (epubUrl, containerId) {
        var book = ePub(epubUrl);
        var rendition = book.renderTo(containerId, {
            width: "100%",
            height: "700px"
        });
        rendition.display();
    },
    openEpubInIframe: function (epubUrl, containerId) {
        console.log("openEpubInIframe called", epubUrl, containerId);
        var container = document.getElementById(containerId);
        container.innerHTML = "";
        var iframe = document.createElement("iframe");
        iframe.style.width = "100%";
        iframe.style.height = "700px";
        iframe.style.border = "none";
        container.appendChild(iframe);

        var doc = iframe.contentDocument || iframe.contentWindow.document;
        doc.open();
        doc.write(`
            <html>
            <head>
                <script src="https://cdn.jsdelivr.net/npm/epubjs/dist/epub.min.js"></script>
            </head>
            <body>
                <div id="viewer" style="width:100%;height:100%;"></div>
                <script>
                    var book = ePub('${epubUrl}');
                    var rendition = book.renderTo("viewer", { width: "100%", height: "100%" });
                    rendition.display();
                <\/script>
            </body>
            </html>
        `);
        doc.close();
    }
};