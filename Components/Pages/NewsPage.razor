@page "/news/{id}"
@using SuntechApp.Data
@using SqlSugar
@using System.Text.RegularExpressions
@using <PERSON>di<PERSON>
@inject ISqlSugarClient db
<style>
    .breadcrumbs-small {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        min-height: 16px !important;
        font-size: 0.85rem !important;
        line-height: 1.2 !important;
    }

        .breadcrumbs-small .mud-breadcrumbs-item {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            min-height: 0 !important;
            font-size: 0.85rem !important;
            line-height: 1.2 !important;
        }

        .breadcrumbs-small .mud-link {
            padding: 0 2px !important;
            font-size: 0.85rem !important;
            line-height: 1.2 !important;
    }
</style>
<MudBreadcrumbs Items="_items" Separator=">" Class="breadcrumbs-small">
    <ItemTemplate Context="item">
        <MudLink Href="@item.Href">@item.Text</MudLink>
    </ItemTemplate>
</MudBreadcrumbs>
<MudPaper Class="pa-6 rounded-20" Elevation="3" Style="max-width: 1200px; margin: 1rem auto">
    <MudCardContent>
        <div class="d-flex justify-space-between align-center mb-4">
            <MudText Typo="Typo.h5" Class="font-weight-black text-primary">
                @news?.Title
            </MudText>
        </div>
        <div class="d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Newspaper" Color="Color.Primary" Size="Size.Medium" Class="mr-4"/>
            <div>
                <MudText Typo="Typo.body2" Class="mb-1 text-primary">
                    <span class="font-weight-medium">作者:</span> @news?.Author
                </MudText>
                <MudText Typo="Typo.body2" Class="text-secondary">
                    <span class="font-weight-medium">发布于:</span> @news?.PublishedDate?.ToString("yyyy-MM-dd")
                </MudText>
            </div>
        </div>
        <hr class="my-6"/>
        <MudText Style="white-space:pre-line;">@news?.Content</MudText>
    </MudCardContent>
</MudPaper>

@code {
    [Parameter] public string? id { get; set; }

    private int? _id;

    private News? news { get; set; }
    private List<BreadcrumbItem> _items= [];
    protected override async Task OnParametersSetAsync() {
        if (int.TryParse(id, out int parsedId)) {
            _id = parsedId;
            news = await db.AsTenant().QueryableWithAttr<News>()
                .Where(n => n.NewsID == _id)
                .FirstAsync();
            _items.Add(new BreadcrumbItem("首页", href: "/"));
            _items.Add(new BreadcrumbItem($"{news.Title}", href: $"/news/{id}"));
        }
    }

    private string FormatContent(string? content) {
        if (string.IsNullOrEmpty(content)) {
            return string.Empty;
        }

        return Regex.Replace(content, @"\r\n|\r|\n", "<br>");
    }

    private RenderFragment RenderContent => builder => {
        var formattedContent = FormatContent(news?.Content);
        var markdown = Markdown.ToHtml(formattedContent); // Markdig library is used here  
        builder.AddMarkupContent(0, markdown);
    };

}
