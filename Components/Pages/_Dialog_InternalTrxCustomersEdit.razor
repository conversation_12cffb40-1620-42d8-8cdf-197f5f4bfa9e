@using SuntechApp.Data
@inject ValueDataMap ValueDataMap

<MudDialog>
    <DialogContent>
        <MudCard Outlined="true">
            <MudCardContent>
                <MudForm>
                    <MudTextField Label="客户编号" @bind-Value="_internalTrxCustomers.CustNum" Required></MudTextField>
                    <MudTextField Label="客户名称" @bind-Value="_internalTrxCustomers.Name" Required></MudTextField>
                    <MudSelect Label="类型" @bind-Value="_internalTrxCustomers.Type" Required>
                        @foreach (var internalTrxCustomersType in _internalTrxCustomersType)
                        {
                            <MudSelectItem Value="internalTrxCustomersType.Type">@internalTrxCustomersType.Display</MudSelectItem>
                        }
                    </MudSelect>
                </MudForm>
            </MudCardContent>
        </MudCard>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color=Color.Primary Variant="Variant.Filled" OnClick="Submit">确定</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter]
    private IMudDialogInstance MudDialog { get; set; }
    private readonly List<InternalTrxCustomersType> _internalTrxCustomersType = ValueDataMap.InternalTrxCustomersTypeMap();
    [Parameter]
    public InternalTrxCustomers _internalTrxCustomers { get; set; }

    private void Submit()
    {
        _internalTrxCustomers.RecordDate = DateTime.Now;
        MudDialog.Close(DialogResult.Ok(_internalTrxCustomers));
    }
    private void Cancel() => MudDialog.Cancel();
}


